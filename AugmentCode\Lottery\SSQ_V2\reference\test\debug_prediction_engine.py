# -*- coding: utf-8 -*-
"""
调试预测引擎内部引用问题
检查预测引擎是否在生成过程中修改了已有的预测数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ssq_lottery_system import SSQLotterySystem
import copy


def debug_prediction_engine():
    """调试预测引擎内部引用问题"""
    print("=== 调试预测引擎内部引用问题 ===")
    
    try:
        # 创建系统实例
        system = SSQLotterySystem("lottery_data_all.xlsx")
        
        # 加载数据
        all_data = system.data_loader.get_current_database()
        
        # 找到期号25067的数据
        target_period = "25067"
        target_index = None
        
        for i, row in all_data.iterrows():
            if str(row['NO']) == target_period:
                target_index = i
                break
        
        if target_index is None:
            print(f"❌ 没有找到期号 {target_period}")
            return False
        
        print(f"✅ 找到期号 {target_period}，索引: {target_index}")
        
        # 获取数据库范围（前99期）
        database_range = 99
        start_index = max(0, target_index - database_range + 1)
        end_index = target_index + 1
        current_database = all_data.iloc[start_index:end_index].copy()
        
        # 获取最新一期数据
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 运行分析器
        print(f"\n=== 运行分析器 ===")
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 逐步生成预测，监控每一步的变化
        print(f"\n=== 逐步生成预测 ===")
        
        # 清空预测引擎的内部状态
        system.prediction_engine.predictions = {}
        system.prediction_engine.database = current_database
        system.prediction_engine.latest_period = latest_period
        
        # 获取筛选要求
        system.prediction_engine._calculate_filter_requirements()
        
        # 第1步：生成基础预测（第1-3组）
        print(f"\n--- 第1步：生成基础预测 ---")
        system.prediction_engine._generate_basic_predictions()
        
        # 保存第2组（马尔科夫链）的快照
        pred_2_after_basic = copy.deepcopy(system.prediction_engine.predictions[2])
        print(f"第2组（马尔科夫链）基础预测:")
        print(f"  红球: {pred_2_after_basic['red_balls']}")
        print(f"  蓝球: {pred_2_after_basic['blue_ball']}")
        
        # 第2步：生成单一筛选预测（第4-6组）
        print(f"\n--- 第2步：生成单一筛选预测 ---")
        system.prediction_engine._generate_single_filter_predictions()
        
        # 检查第2组是否被修改
        pred_2_after_single = system.prediction_engine.predictions[2]
        print(f"第2组在单一筛选后:")
        print(f"  红球: {pred_2_after_single['red_balls']}")
        print(f"  蓝球: {pred_2_after_single['blue_ball']}")
        print(f"  是否相同对象: {pred_2_after_basic is pred_2_after_single}")
        print(f"  内容是否相等: {pred_2_after_basic == pred_2_after_single}")
        
        # 第3步：生成组合筛选预测（第7-24组）
        print(f"\n--- 第3步：生成组合筛选预测 ---")
        
        # 在生成第9组之前，再次检查第2组
        pred_2_before_9 = copy.deepcopy(system.prediction_engine.predictions[2])
        print(f"生成第9组前，第2组状态:")
        print(f"  红球: {pred_2_before_9['red_balls']}")
        print(f"  蓝球: {pred_2_before_9['blue_ball']}")
        
        # 单独生成第9组
        print(f"\n--- 单独生成第9组 ---")
        pred_9 = system.prediction_engine._predict_with_repeat_filter(
            system.prediction_engine.predictions[2], 'markov'
        )
        print(f"第9组预测结果:")
        print(f"  红球: {pred_9['red_balls']}")
        print(f"  蓝球: {pred_9['blue_ball']}")
        print(f"  方法: {pred_9['method']}")
        
        # 检查生成第9组后，第2组是否被修改
        pred_2_after_9 = system.prediction_engine.predictions[2]
        print(f"\n生成第9组后，第2组状态:")
        print(f"  红球: {pred_2_after_9['red_balls']}")
        print(f"  蓝球: {pred_2_after_9['blue_ball']}")
        print(f"  与生成前相同: {pred_2_before_9 == pred_2_after_9}")
        
        # 将第9组存储到预测引擎
        system.prediction_engine.predictions[9] = pred_9
        
        # 继续生成其他组合筛选预测
        system.prediction_engine._generate_combined_filter_predictions()
        
        # 最终检查第2组和第9组
        print(f"\n--- 最终检查 ---")
        final_pred_2 = system.prediction_engine.predictions[2]
        final_pred_9 = system.prediction_engine.predictions[9]
        
        print(f"最终第2组:")
        print(f"  红球: {final_pred_2['red_balls']}")
        print(f"  蓝球: {final_pred_2['blue_ball']}")
        
        print(f"最终第9组:")
        print(f"  红球: {final_pred_9['red_balls']}")
        print(f"  蓝球: {final_pred_9['blue_ball']}")
        
        # 检查内存地址
        print(f"\n--- 内存地址检查 ---")
        print(f"第2组 red_balls id: {id(final_pred_2['red_balls'])}")
        print(f"第9组 red_balls id: {id(final_pred_9['red_balls'])}")
        print(f"是否共享红球列表: {final_pred_2['red_balls'] is final_pred_9['red_balls']}")
        
        # 模拟修改第2组，看是否影响第9组
        print(f"\n--- 模拟修改测试 ---")
        print(f"修改前第2组红球[0]: {final_pred_2['red_balls'][0]}")
        print(f"修改前第9组红球[0]: {final_pred_9['red_balls'][0]}")
        
        old_value = final_pred_2['red_balls'][0]
        final_pred_2['red_balls'][0] = 99
        
        print(f"修改后第2组红球[0]: {final_pred_2['red_balls'][0]}")
        print(f"修改后第9组红球[0]: {final_pred_9['red_balls'][0]}")
        
        if final_pred_2['red_balls'][0] == final_pred_9['red_balls'][0]:
            print("❌ 发现引用问题！修改第2组影响了第9组")
        else:
            print("✅ 没有引用问题")
        
        # 恢复原值
        final_pred_2['red_balls'][0] = old_value
        
        # 检查与用户截图的对比
        print(f"\n--- 与用户截图对比 ---")
        expected_red = [2, 7, 10, 22, 27, 33]
        expected_blue = 14
        
        actual_red = sorted([int(x) for x in final_pred_9['red_balls']])
        actual_blue = int(final_pred_9['blue_ball'])
        
        print(f"期望红球: {expected_red}")
        print(f"实际红球: {actual_red}")
        print(f"红球匹配: {'✅' if actual_red == expected_red else '❌'}")
        
        print(f"期望蓝球: {expected_blue}")
        print(f"实际蓝球: {actual_blue}")
        print(f"蓝球匹配: {'✅' if actual_blue == expected_blue else '❌'}")
        
        # 检查是否存在其他地方修改了预测数据
        print(f"\n--- 检查其他可能的修改点 ---")
        
        # 重新生成完整预测
        fresh_predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
        fresh_pred_9 = fresh_predictions[9]
        
        print(f"重新生成的第9组:")
        print(f"  红球: {fresh_pred_9['red_balls']}")
        print(f"  蓝球: {fresh_pred_9['blue_ball']}")
        print(f"  与之前相同: {fresh_pred_9 == final_pred_9}")
        
        return True
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    debug_prediction_engine()
