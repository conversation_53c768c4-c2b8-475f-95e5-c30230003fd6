#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行完整的分析流程
模拟用户的操作：从25001期开始，数据库范围99期
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def run_full_analysis():
    """运行完整的分析流程"""
    print("=== 运行完整的分析流程 ===")
    print("模拟用户操作：从25001期开始，数据库范围99期")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    try:
        # 用户的参数
        start_period = '25001'
        database_range = 99
        
        print(f"开始期号: {start_period}")
        print(f"数据库范围: {database_range}期")
        
        # 获取分析期号列表
        analysis_periods = system.data_loader.get_analysis_periods(start_period)
        
        # 只分析前3期，避免运行时间过长
        test_periods = analysis_periods[:3]
        print(f"测试期号: {test_periods}")
        
        results = []
        
        for i, period in enumerate(test_periods):
            print(f"\n正在分析期号 {period} ({i+1}/{len(test_periods)})...")
            
            # 获取当前数据库
            current_database = system.data_loader.get_database_for_period(
                period, database_range
            )
            
            if current_database is None or len(current_database) == 0:
                print(f"  跳过期号 {period}：无数据库数据")
                continue
            
            # 获取答案数据
            answer_data = system.data_loader.get_answer_data(period)
            
            if answer_data is None or len(answer_data) == 0:
                print(f"  跳过期号 {period}：无答案数据")
                continue
            
            # 运行预测
            latest_period = system.data_loader.get_latest_period(current_database)
            
            # 分析
            system.statistical_analyzer.analyze(current_database)
            system.markov_analyzer.analyze(current_database, latest_period)
            system.bayesian_analyzer.analyze(current_database, latest_period)
            
            # 生成预测
            predictions = system.prediction_engine.generate_all_predictions(
                current_database, latest_period
            )
            
            # 比对结果
            comparison_result = system.comparison_engine.compare_predictions(
                predictions, answer_data
            )
            
            results.append({
                'period': period,
                'predictions': predictions,
                'comparison': comparison_result,
                'database_size': len(current_database),
                'latest_period': latest_period
            })
            
            # 检查是否有高命中数
            max_hits_in_period = 0
            high_hit_groups = []
            
            for group_id in range(1, 25):
                if group_id in comparison_result:
                    group_result = comparison_result[group_id]
                    max_hit = group_result['max_hit']
                    max_hits_in_period = max(max_hits_in_period, max_hit['total_hits'])
                    
                    if max_hit['total_hits'] >= 6:
                        high_hit_groups.append((group_id, predictions[group_id], max_hit))
            
            print(f"  期号 {period} 最大命中数: {max_hits_in_period}球")
            if high_hit_groups:
                print(f"  发现 {len(high_hit_groups)} 个高命中组:")
                for group_id, prediction, max_hit in high_hit_groups:
                    red_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
                    print(f"    第{group_id}组: {red_str} + {prediction['blue_ball']} -> {max_hit['total_hits']}球")
        
        print(f"\n分析完成！共分析了 {len(results)} 期")
        
        # 调用高命中数详细信息打印功能
        print(f"\n=== 调用高命中数详细信息打印功能 ===")
        system._print_high_hit_details(results)
        
        return True
        
    except Exception as e:
        print(f"分析过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("运行完整的分析流程")
    print("=" * 60)
    
    success = run_full_analysis()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 分析完成！")
        print("请检查上面的高命中数详细信息打印结果")
        print("看是否与您在终端中看到的结果一致")
    else:
        print("❌ 分析失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
