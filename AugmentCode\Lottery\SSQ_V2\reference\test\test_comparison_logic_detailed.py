#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细测试比对逻辑
验证每个预测是否与所有6期答案数据进行比对
"""

import sys
from modules.comparison_engine import ComparisonEngine
from modules.data_loader import DataLoader


def test_comparison_with_6_periods():
    """测试与6期答案数据的比对"""
    print("=== 测试与6期答案数据的比对 ===")
    
    # 初始化数据加载器
    data_loader = DataLoader('lottery_data_all.xlsx')
    
    # 测试期号
    test_period = '25001'
    
    # 获取答案数据
    answer_data = data_loader.get_answer_data(test_period)
    
    if not answer_data:
        print(f"❌ 无法获取 {test_period} 期的答案数据")
        return False
    
    print(f"获取到 {len(answer_data)} 期答案数据:")
    for i, answer in enumerate(answer_data):
        red_str = ' '.join(map(str, answer['red_balls']))
        print(f"  第{i+1}期: {answer['period']} {red_str} + {answer['blue_ball']}")
    
    if len(answer_data) != 6:
        print(f"❌ 答案数据期数不正确: 期望6期, 实际{len(answer_data)}期")
        return False
    
    # 创建测试预测
    test_prediction = {
        'red_balls': [1, 2, 3, 4, 5, 6],
        'blue_ball': 7,
        'method': '测试预测'
    }
    
    print(f"\n测试预测: {test_prediction['red_balls']} + {test_prediction['blue_ball']}")
    
    # 使用比对引擎
    config = {}
    engine = ComparisonEngine(config)
    
    # 比对单个预测
    results = engine._compare_single_prediction(test_prediction, answer_data)
    
    print(f"\n比对结果:")
    if len(results) != 6:
        print(f"❌ 比对结果期数不正确: 期望6期, 实际{len(results)}期")
        return False
    
    max_hits = 0
    max_period = None
    
    for result in results:
        print(f"  期号{result['period']}: 红{result['red_hits']}+蓝{result['blue_hits']} = {result['total_hits']}球")
        if result['total_hits'] > max_hits:
            max_hits = result['total_hits']
            max_period = result['period']
    
    print(f"\n最大命中: {max_hits}球 (期号{max_period})")
    
    # 测试完整的比对流程
    predictions = {'group_1': test_prediction}
    comparison_results = engine.compare_predictions(predictions, answer_data)
    
    if 'group_1' in comparison_results:
        group_result = comparison_results['group_1']
        max_hit = group_result['max_hit']
        
        print(f"\n完整比对流程结果:")
        print(f"  最大命中: {max_hit['total_hits']}球")
        print(f"  最大命中期号: {max_hit['period']}")
        print(f"  红球命中数: {max_hit['red_hits']}")
        print(f"  蓝球命中数: {max_hit['blue_hits']}")
        
        # 验证是否选择了正确的最大命中
        if max_hit['total_hits'] == max_hits and max_hit['period'] == max_period:
            print(f"  ✅ 最大命中选择正确")
            return True
        else:
            print(f"  ❌ 最大命中选择错误")
            return False
    else:
        print(f"❌ 完整比对流程没有返回结果")
        return False


def test_real_case_with_detailed_output():
    """测试真实案例并输出详细信息"""
    print(f"\n=== 测试真实案例并输出详细信息 ===")
    
    # 初始化数据加载器
    data_loader = DataLoader('lottery_data_all.xlsx')
    
    # 测试25067期
    test_period = '25067'
    answer_data = data_loader.get_answer_data(test_period)
    
    if not answer_data:
        print(f"❌ 无法获取 {test_period} 期的答案数据")
        return False
    
    print(f"25067期后的6期答案数据:")
    for i, answer in enumerate(answer_data):
        red_str = ' '.join(map(str, answer['red_balls']))
        print(f"  第{i+1}期: {answer['period']} {red_str} + {answer['blue_ball']}")
    
    # 用户提到的预测号码
    test_prediction = {
        'red_balls': [2, 7, 10, 22, 27, 33],
        'blue_ball': 14,
        'method': '用户案例'
    }
    
    print(f"\n用户案例预测: {test_prediction['red_balls']} + {test_prediction['blue_ball']}")
    
    # 使用比对引擎
    config = {}
    engine = ComparisonEngine(config)
    
    # 详细比对每一期
    results = engine._compare_single_prediction(test_prediction, answer_data)
    
    print(f"\n详细比对结果:")
    max_hits = 0
    max_period = None
    max_result = None
    
    for result in results:
        red_str = ' '.join(map(str, result['answer_red_balls']))
        print(f"  期号{result['period']}: {red_str} + {result['answer_blue_ball']}")
        print(f"    红球命中: {result['red_hits']}, 蓝球命中: {result['blue_hits']}, 总命中: {result['total_hits']}")
        
        if result['total_hits'] > max_hits:
            max_hits = result['total_hits']
            max_period = result['period']
            max_result = result
    
    print(f"\n最大命中分析:")
    print(f"  最大命中: {max_hits}球")
    print(f"  最大命中期号: {max_period}")
    
    if max_result:
        # 手动验证最大命中期号
        pred_red_set = set(test_prediction['red_balls'])
        answer_red_set = set(max_result['answer_red_balls'])
        red_hits_manual = len(pred_red_set & answer_red_set)
        blue_hits_manual = 1 if test_prediction['blue_ball'] == max_result['answer_blue_ball'] else 0
        total_hits_manual = red_hits_manual + blue_hits_manual
        
        print(f"\n手动验证最大命中期号 {max_period}:")
        print(f"  预测红球集合: {pred_red_set}")
        print(f"  实际红球集合: {answer_red_set}")
        print(f"  红球交集: {pred_red_set & answer_red_set}")
        print(f"  手动计算总命中数: {total_hits_manual}")
        print(f"  引擎计算总命中数: {max_result['total_hits']}")
        
        if max_result['total_hits'] == total_hits_manual:
            print(f"  ✅ 最大命中计算正确")
            return True
        else:
            print(f"  ❌ 最大命中计算错误")
            return False
    
    return False


def test_excel_export_logic():
    """测试Excel导出逻辑"""
    print(f"\n=== 测试Excel导出逻辑 ===")
    
    # 模拟Excel导出中使用的数据结构
    predictions = {
        1: {
            'red_balls': [2, 7, 10, 22, 27, 33],
            'blue_ball': 14,
            'method': '测试方法'
        }
    }
    
    answer_data = [
        {'period': '25068', 'red_balls': [1, 5, 12, 18, 25, 28], 'blue_ball': 7},
        {'period': '25069', 'red_balls': [2, 14, 17, 25, 27, 29], 'blue_ball': 5},
        {'period': '25070', 'red_balls': [2, 3, 15, 21, 22, 33], 'blue_ball': 6},
        {'period': '25071', 'red_balls': [1, 12, 18, 23, 25, 28], 'blue_ball': 7},
        {'period': '25072', 'red_balls': [2, 14, 17, 25, 27, 29], 'blue_ball': 5},
        {'period': '25073', 'red_balls': [2, 7, 10, 27, 30, 33], 'blue_ball': 11}
    ]
    
    config = {}
    engine = ComparisonEngine(config)
    
    # 执行比对
    comparison_results = engine.compare_predictions(predictions, answer_data)
    
    if 1 in comparison_results:
        group_result = comparison_results[1]
        max_hit = group_result['max_hit']
        
        print(f"Excel导出将显示的数据:")
        print(f"  分析期号: 25067")
        print(f"  预测组号: 1")
        print(f"  预测方法: {predictions[1]['method']}")
        print(f"  预测红球: {' '.join(map(str, predictions[1]['red_balls']))}")
        print(f"  预测蓝球: {predictions[1]['blue_ball']}")
        print(f"  最大命中球数: {max_hit['total_hits']}")
        print(f"  最大命中期号: {max_hit['period']}")
        print(f"  红球命中数: {max_hit['red_hits']}")
        print(f"  蓝球命中数: {max_hit['blue_hits']}")
        print(f"  蓝球命中状态: {'是' if max_hit['blue_hits'] == 1 else '否'}")
        
        # 验证是否正确选择了25073期作为最大命中
        if max_hit['period'] == '25073' and max_hit['total_hits'] == 5:
            print(f"  ✅ Excel导出数据正确")
            return True
        else:
            print(f"  ❌ Excel导出数据错误")
            return False
    
    return False


def main():
    """主函数"""
    print("详细测试比对逻辑")
    print("=" * 60)
    
    success1 = test_comparison_with_6_periods()
    success2 = test_real_case_with_detailed_output()
    success3 = test_excel_export_logic()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3:
        print("🎉 所有比对逻辑测试通过！")
        print("比对引擎正确处理了与6期答案数据的比对")
        print("最大命中数选择逻辑正确")
    else:
        print("❌ 比对逻辑测试失败，需要修正")
    
    return success1 and success2 and success3


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
