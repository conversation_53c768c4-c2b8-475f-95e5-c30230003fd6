#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试25067期的具体案例
验证用户指出的命中数计算问题
"""

import sys
from modules.comparison_engine import ComparisonEngine
from modules.data_loader import DataLoader


def test_25067_case_with_real_data():
    """使用真实数据测试25067期案例"""
    print("=== 使用真实数据测试25067期案例 ===")
    
    # 初始化数据加载器
    data_loader = DataLoader('lottery_data_all.xlsx')
    
    # 获取25067期之后的答案数据
    start_period = '25068'
    answer_data = data_loader.get_answer_data(start_period)
    
    print(f"获取到 {len(answer_data)} 期答案数据")
    
    # 用户提供的25067期预测
    prediction = {
        'red_balls': [2, 7, 10, 22, 27, 33],
        'blue_ball': 14,
        'method': '9号小冷大球红球量5加法'
    }
    
    print(f"25067期预测号码: {prediction['red_balls']} + {prediction['blue_ball']}")
    
    # 使用比对引擎
    config = {}
    engine = ComparisonEngine(config)
    
    # 比对单个预测
    results = engine._compare_single_prediction(prediction, answer_data)
    
    print(f"\n所有期号的比对结果:")
    max_hits = 0
    max_period = None
    max_result = None
    
    for result in results:
        print(f"  期号{result['period']}: 红{result['red_hits']}+蓝{result['blue_hits']} = {result['total_hits']}球")
        if result['total_hits'] > max_hits:
            max_hits = result['total_hits']
            max_period = result['period']
            max_result = result
    
    print(f"\n最大命中: {max_hits}球 (期号{max_period})")
    
    if max_result:
        print(f"\n最大命中期号详细信息:")
        print(f"  期号: {max_result['period']}")
        print(f"  实际号码: {max_result['answer_red_balls']} + {max_result['answer_blue_ball']}")
        print(f"  红球命中数: {max_result['red_hits']}")
        print(f"  蓝球命中数: {max_result['blue_hits']}")
        print(f"  总命中数: {max_result['total_hits']}")
        print(f"  蓝球命中状态: {max_result['blue_hit_status']}")
        
        # 手动验证最大命中期号
        if max_result['period'] == '25073':
            # 验证25073期的计算
            pred_red_set = set(prediction['red_balls'])
            answer_red_set = set(max_result['answer_red_balls'])
            
            red_hits_manual = len(pred_red_set & answer_red_set)
            blue_hits_manual = 1 if prediction['blue_ball'] == max_result['answer_blue_ball'] else 0
            total_hits_manual = red_hits_manual + blue_hits_manual
            
            print(f"\n手动验证25073期:")
            print(f"  预测红球集合: {pred_red_set}")
            print(f"  实际红球集合: {answer_red_set}")
            print(f"  红球交集: {pred_red_set & answer_red_set}")
            print(f"  手动计算红球命中数: {red_hits_manual}")
            print(f"  手动计算蓝球命中数: {blue_hits_manual}")
            print(f"  手动计算总命中数: {total_hits_manual}")
            
            if (max_result['red_hits'] == red_hits_manual and 
                max_result['blue_hits'] == blue_hits_manual and 
                max_result['total_hits'] == total_hits_manual == 5):
                print(f"  ✅ 25073期计算正确: 5球命中")
                return True
            else:
                print(f"  ❌ 25073期计算错误")
                return False
        else:
            print(f"  ⚠️ 最大命中期号不是25073，而是{max_result['period']}")
            return False
    
    return False


def test_25067_case_with_mock_data():
    """使用模拟数据测试25067期案例"""
    print(f"\n=== 使用模拟数据测试25067期案例 ===")
    
    # 用户提供的25067期预测
    prediction = {
        'red_balls': [2, 7, 10, 22, 27, 33],
        'blue_ball': 14,
        'method': '9号小冷大球红球量5加法'
    }
    
    # 模拟答案数据，包含25073期
    answer_data = [
        {
            'period': '25068',
            'red_balls': [1, 5, 12, 18, 25, 28],
            'blue_ball': 7
        },
        {
            'period': '25069',
            'red_balls': [2, 14, 17, 25, 27, 29],
            'blue_ball': 5
        },
        {
            'period': '25070',
            'red_balls': [2, 3, 15, 21, 22, 33],
            'blue_ball': 6
        },
        {
            'period': '25071',
            'red_balls': [1, 12, 18, 23, 25, 28],
            'blue_ball': 7
        },
        {
            'period': '25072',
            'red_balls': [2, 14, 17, 25, 27, 29],
            'blue_ball': 5
        },
        {
            'period': '25073',
            'red_balls': [2, 7, 10, 27, 30, 33],
            'blue_ball': 11
        }
    ]
    
    print(f"25067期预测号码: {prediction['red_balls']} + {prediction['blue_ball']}")
    
    # 使用比对引擎
    config = {}
    engine = ComparisonEngine(config)
    
    # 完整的比对流程
    predictions = {'group_1': prediction}
    comparison_results = engine.compare_predictions(predictions, answer_data)
    
    print(f"\n完整比对结果:")
    for group_id, result in comparison_results.items():
        max_hit = result['max_hit']
        print(f"  {group_id}:")
        print(f"    最大命中: {max_hit['total_hits']}球")
        print(f"    最大命中期号: {max_hit['period']}")
        print(f"    红球命中数: {max_hit['red_hits']}")
        print(f"    蓝球命中数: {max_hit['blue_hits']}")
        
        if max_hit['period'] == '25073' and max_hit['total_hits'] == 5:
            print(f"    ✅ 结果正确: 25073期5球命中")
            return True
        elif max_hit['period'] == '25073' and max_hit['total_hits'] == 6:
            print(f"    ❌ 结果错误: 25073期显示6球命中，应该是5球")
            return False
        else:
            print(f"    ⚠️ 意外结果: 期号{max_hit['period']}, 命中{max_hit['total_hits']}球")
            return False
    
    return False


def main():
    """主函数"""
    print("25067期案例测试")
    print("=" * 60)
    
    success1 = test_25067_case_with_real_data()
    success2 = test_25067_case_with_mock_data()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 25067期案例测试通过！")
        print("命中数计算正确：25073期5球命中（红球5+蓝球0）")
        print("\n如果Excel显示6球，可能的原因：")
        print("1. Excel文件是旧版本生成的")
        print("2. 数据传递过程中有错误")
        print("3. 需要重新运行分析生成新的Excel")
    else:
        print("❌ 25067期案例测试失败")
        print("需要进一步调查问题原因")
    
    return success1 and success2


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
