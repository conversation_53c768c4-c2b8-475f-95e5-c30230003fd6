#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试期号选择逻辑修正
验证当有多个期号命中数相同时，选择期号最小的
"""

import sys
from modules.comparison_engine import ComparisonEngine


def test_period_selection_logic():
    """测试期号选择逻辑"""
    print("=== 测试期号选择逻辑 ===")
    
    # 构造测试数据：多个期号有相同的最大命中数
    prediction = {
        'red_balls': [1, 2, 3, 4, 5, 6],
        'blue_ball': 7,
        'method': '测试方法'
    }
    
    # 答案数据：多个期号都有相同的命中数
    answer_data = [
        {
            'period': '25001',
            'red_balls': [1, 2, 3, 10, 11, 12],  # 命中3个红球
            'blue_ball': 8  # 蓝球不命中
        },
        {
            'period': '25002',
            'red_balls': [1, 2, 10, 11, 12, 13],  # 命中2个红球
            'blue_ball': 7  # 蓝球命中
        },
        {
            'period': '25003',
            'red_balls': [1, 2, 3, 10, 11, 12],  # 命中3个红球
            'blue_ball': 8  # 蓝球不命中
        },
        {
            'period': '25004',
            'red_balls': [4, 5, 6, 10, 11, 12],  # 命中3个红球
            'blue_ball': 8  # 蓝球不命中
        },
        {
            'period': '25005',
            'red_balls': [1, 10, 11, 12, 13, 14],  # 命中1个红球
            'blue_ball': 7  # 蓝球命中
        },
        {
            'period': '25006',
            'red_balls': [10, 11, 12, 13, 14, 15],  # 命中0个红球
            'blue_ball': 8  # 蓝球不命中
        }
    ]
    
    print("测试数据:")
    print(f"  预测: {prediction['red_balls']} + {prediction['blue_ball']}")
    print(f"  答案数据:")
    
    expected_hits = []
    for answer in answer_data:
        pred_red_set = set(prediction['red_balls'])
        answer_red_set = set(answer['red_balls'])
        red_hits = len(pred_red_set & answer_red_set)
        blue_hits = 1 if prediction['blue_ball'] == answer['blue_ball'] else 0
        total_hits = red_hits + blue_hits
        expected_hits.append((answer['period'], total_hits, red_hits, blue_hits))
        
        red_str = ' '.join(map(str, answer['red_balls']))
        print(f"    期号{answer['period']}: {red_str} + {answer['blue_ball']} -> 红{red_hits}+蓝{blue_hits}={total_hits}球")
    
    # 找到最大命中数
    max_total_hits = max(hit[1] for hit in expected_hits)
    max_hit_periods = [hit for hit in expected_hits if hit[1] == max_total_hits]
    
    print(f"\n  最大命中数: {max_total_hits}球")
    print(f"  最大命中期号: {[hit[0] for hit in max_hit_periods]}")
    print(f"  期望选择: {min(hit[0] for hit in max_hit_periods)} (期号最小)")
    
    # 使用修正后的比对引擎
    config = {}
    engine = ComparisonEngine(config)
    
    predictions = {1: prediction}
    comparison_results = engine.compare_predictions(predictions, answer_data)
    
    if 1 in comparison_results:
        group_result = comparison_results[1]
        max_hit = group_result['max_hit']
        
        print(f"\n修正后的比对结果:")
        print(f"  选择的期号: {max_hit['period']}")
        print(f"  命中球数: {max_hit['total_hits']}")
        print(f"  红球命中数: {max_hit['red_hits']}")
        print(f"  蓝球命中数: {max_hit['blue_hits']}")
        
        # 验证是否选择了期号最小的
        expected_period = min(hit[0] for hit in max_hit_periods)
        
        if max_hit['period'] == expected_period:
            print(f"  ✅ 正确选择了期号最小的: {expected_period}")
            return True
        else:
            print(f"  ❌ 错误选择了期号: {max_hit['period']}, 期望: {expected_period}")
            return False
    else:
        print(f"❌ 没有比对结果")
        return False


def test_user_case_with_fix():
    """测试用户案例的修正"""
    print(f"\n=== 测试用户案例的修正 ===")
    
    # 用户案例：分析25067期，数据库范围100期
    # 预测号码：[2, 7, 10, 22, 27, 33] + 14
    # 答案数据：25068-25073期
    
    prediction = {
        'red_balls': [2, 7, 10, 22, 27, 33],
        'blue_ball': 14,
        'method': '测试方法'
    }
    
    # 模拟25068-25073期的答案数据
    answer_data = [
        {'period': '25068', 'red_balls': [1, 5, 8, 15, 20, 25], 'blue_ball': 3},
        {'period': '25069', 'red_balls': [3, 9, 12, 18, 24, 30], 'blue_ball': 6},
        {'period': '25070', 'red_balls': [4, 11, 16, 21, 26, 31], 'blue_ball': 9},
        {'period': '25071', 'red_balls': [6, 13, 17, 23, 28, 32], 'blue_ball': 12},
        {'period': '25072', 'red_balls': [2, 7, 14, 19, 29, 33], 'blue_ball': 15},  # 命中3个红球
        {'period': '25073', 'red_balls': [2, 7, 10, 27, 30, 33], 'blue_ball': 11}   # 命中5个红球
    ]
    
    print("用户案例数据:")
    print(f"  预测: {prediction['red_balls']} + {prediction['blue_ball']}")
    print(f"  答案数据:")
    
    for answer in answer_data:
        pred_red_set = set(prediction['red_balls'])
        answer_red_set = set(answer['red_balls'])
        red_hits = len(pred_red_set & answer_red_set)
        blue_hits = 1 if prediction['blue_ball'] == answer['blue_ball'] else 0
        total_hits = red_hits + blue_hits
        
        red_str = ' '.join(map(str, answer['red_balls']))
        print(f"    期号{answer['period']}: {red_str} + {answer['blue_ball']} -> 红{red_hits}+蓝{blue_hits}={total_hits}球")
    
    # 使用修正后的比对引擎
    config = {}
    engine = ComparisonEngine(config)
    
    predictions = {1: prediction}
    comparison_results = engine.compare_predictions(predictions, answer_data)
    
    if 1 in comparison_results:
        group_result = comparison_results[1]
        max_hit = group_result['max_hit']
        
        print(f"\n修正后的比对结果:")
        print(f"  最大命中期号: {max_hit['period']}")
        print(f"  最大命中球数: {max_hit['total_hits']}")
        print(f"  红球命中数: {max_hit['red_hits']}")
        print(f"  蓝球命中数: {max_hit['blue_hits']}")
        print(f"  蓝球命中状态: {'是' if max_hit['blue_hits'] == 1 else '否'}")
        
        # 验证是否选择了25073期（最大命中数5球）
        if max_hit['period'] == '25073' and max_hit['total_hits'] == 5:
            print(f"  ✅ 正确选择了最大命中期号25073")
            return True
        else:
            print(f"  ❌ 选择错误")
            return False
    else:
        print(f"❌ 没有比对结果")
        return False


def main():
    """主函数"""
    print("测试期号选择逻辑修正")
    print("=" * 60)
    
    success1 = test_period_selection_logic()
    success2 = test_user_case_with_fix()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 期号选择逻辑修正成功！")
        print("\n修正总结:")
        print("1. ✅ 当有多个期号命中数相同时，现在选择期号最小的")
        print("2. ✅ 用户案例验证通过")
        print("3. ✅ 比对逻辑符合用户需求")
    else:
        print("❌ 期号选择逻辑仍有问题")
    
    return success1 and success2


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
