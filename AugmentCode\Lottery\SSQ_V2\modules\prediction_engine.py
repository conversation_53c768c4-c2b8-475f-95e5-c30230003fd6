# -*- coding: utf-8 -*-
"""
预测引擎模块 (Prediction Engine Module)

负责实现预测算法，结合历史出现概率和马尔科夫链算法生成预测号码。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional


class PredictionEngine:
    """
    预测引擎类
    
    负责生成预测号码
    """
    
    def __init__(self, config: Dict, statistical_analyzer, markov_analyzer):
        """
        初始化预测引擎
        
        Args:
            config: 系统配置字典
            statistical_analyzer: 统计分析器
            markov_analyzer: 马尔科夫链分析器
        """
        self.config = config
        self.statistical_analyzer = statistical_analyzer
        self.markov_analyzer = markov_analyzer
    
    def generate_predictions(self, long_term_stats: Dict, short_term_stats: Dict,
                           long_term_markov: Dict, short_term_markov: Dict,
                           latest_period: Dict) -> Dict:
        """
        生成预测号码
        
        Args:
            long_term_stats: 远期统计分析结果
            short_term_stats: 近期统计分析结果
            long_term_markov: 远期马尔科夫链分析结果
            short_term_markov: 近期马尔科夫链分析结果
            latest_period: 最新一期数据
            
        Returns:
            预测结果字典
        """
        predictions = {}
        
        # 第1组：历史出现概率复式
        predictions[1] = self._predict_by_historical_probability(
            long_term_stats, short_term_stats
        )
        
        # 第2组：马尔科夫链复式
        predictions[2] = self._predict_by_markov_chain(
            long_term_markov, short_term_markov
        )
        
        return predictions
    
    def _predict_by_historical_probability(self, long_term_stats: Dict,
                                         short_term_stats: Dict) -> Dict:
        """
        第1组预测算法：历史出现概率复式

        Args:
            long_term_stats: 远期统计分析结果
            short_term_stats: 近期统计分析结果

        Returns:
            预测结果字典
        """
        # 1）从远期数据库中选出红球历史出现概率最大的6个红球号码
        long_term_red_probs = long_term_stats['red_ball_probabilities']
        long_term_red_sorted = sorted(
            long_term_red_probs.items(),
            key=lambda x: x[1],
            reverse=True
        )
        long_term_red_balls = [ball for ball, prob in long_term_red_sorted[:6]]

        # 1）从远期数据库中选出蓝球历史出现概率最大的1个蓝球号码
        long_term_blue_probs = long_term_stats['blue_ball_probabilities']
        long_term_blue_sorted = sorted(
            long_term_blue_probs.items(),
            key=lambda x: x[1],
            reverse=True
        )
        long_term_blue_balls = [ball for ball, prob in long_term_blue_sorted[:1]]

        # 2）从近期数据库中选出与远期不重复的红球历史出现概率最大的1个红球号码
        short_term_red_probs = short_term_stats['red_ball_probabilities']
        short_term_red_sorted = sorted(
            short_term_red_probs.items(),
            key=lambda x: x[1],
            reverse=True
        )

        # 找出与远期红球不重复的近期红球
        short_term_red_balls = []
        for ball, prob in short_term_red_sorted:
            if ball not in long_term_red_balls:
                short_term_red_balls.append(ball)
                break  # 只选1个

        # 2）从近期数据库中选出与远期不重复的蓝球历史出现概率最大的1个蓝球号码
        short_term_blue_probs = short_term_stats['blue_ball_probabilities']
        short_term_blue_sorted = sorted(
            short_term_blue_probs.items(),
            key=lambda x: x[1],
            reverse=True
        )

        # 找出与远期蓝球不重复的近期蓝球
        short_term_blue_balls = []
        for ball, prob in short_term_blue_sorted:
            if ball not in long_term_blue_balls:
                short_term_blue_balls.append(ball)
                break  # 只选1个

        # 3）合并红球号码并按从小到大排序
        all_red_balls = long_term_red_balls + short_term_red_balls
        all_red_balls.sort()

        # 3）合并蓝球号码并按从小到大排序
        all_blue_balls = long_term_blue_balls + short_term_blue_balls
        all_blue_balls.sort()

        return {
            'method': '历史出现概率复式',
            'red_balls': all_red_balls,
            'blue_balls': all_blue_balls,
            'details': {
                'long_term_red': long_term_red_balls,
                'long_term_blue': long_term_blue_balls,
                'short_term_red': short_term_red_balls,
                'short_term_blue': short_term_blue_balls
            }
        }
    
    def _predict_by_markov_chain(self, long_term_markov: Dict,
                               short_term_markov: Dict) -> Dict:
        """
        第2组预测算法：马尔科夫链复式

        Args:
            long_term_markov: 远期马尔科夫链分析结果
            short_term_markov: 近期马尔科夫链分析结果

        Returns:
            预测结果字典
        """
        # 1）从远期数据库马尔科夫链中选出红球迁移概率最大的6个红球号码
        long_term_red_markov = long_term_markov['red_markov_probabilities']
        if long_term_red_markov is not None:
            long_term_red_indices = np.argsort(long_term_red_markov)[::-1][:6]
            long_term_red_balls = [idx + 1 for idx in long_term_red_indices]
        else:
            long_term_red_balls = []

        # 1）从远期数据库马尔科夫链中选出蓝球迁移概率最大的1个蓝球号码
        long_term_blue_markov = long_term_markov['blue_markov_probabilities']
        if long_term_blue_markov is not None:
            long_term_blue_indices = np.argsort(long_term_blue_markov)[::-1][:1]
            long_term_blue_balls = [idx + 1 for idx in long_term_blue_indices]
        else:
            long_term_blue_balls = []

        # 2）从近期数据库马尔科夫链中选出与远期不重复的红球迁移概率最大的1个红球号码
        short_term_red_markov = short_term_markov['red_markov_probabilities']
        short_term_red_balls = []
        if short_term_red_markov is not None:
            # 按概率从高到低排序
            short_term_red_indices = np.argsort(short_term_red_markov)[::-1]
            # 找出与远期红球不重复的近期红球
            for idx in short_term_red_indices:
                ball = idx + 1
                if ball not in long_term_red_balls:
                    short_term_red_balls.append(ball)
                    break  # 只选1个

        # 2）从近期数据库马尔科夫链中选出与远期不重复的蓝球迁移概率最大的1个蓝球号码
        short_term_blue_markov = short_term_markov['blue_markov_probabilities']
        short_term_blue_balls = []
        if short_term_blue_markov is not None:
            # 按概率从高到低排序
            short_term_blue_indices = np.argsort(short_term_blue_markov)[::-1]
            # 找出与远期蓝球不重复的近期蓝球
            for idx in short_term_blue_indices:
                ball = idx + 1
                if ball not in long_term_blue_balls:
                    short_term_blue_balls.append(ball)
                    break  # 只选1个

        # 3）合并红球号码并按从小到大排序
        all_red_balls = long_term_red_balls + short_term_red_balls
        all_red_balls.sort()

        # 3）合并蓝球号码并按从小到大排序
        all_blue_balls = long_term_blue_balls + short_term_blue_balls
        all_blue_balls.sort()

        return {
            'method': '马尔科夫链复式',
            'red_balls': all_red_balls,
            'blue_balls': all_blue_balls,
            'details': {
                'long_term_red': long_term_red_balls,
                'long_term_blue': long_term_blue_balls,
                'short_term_red': short_term_red_balls,
                'short_term_blue': short_term_blue_balls
            }
        }
    
    def format_prediction_display(self, prediction: Dict) -> str:
        """
        格式化预测结果显示
        
        Args:
            prediction: 预测结果字典
            
        Returns:
            格式化的显示字符串
        """
        red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
        blue_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['blue_balls'])
        
        return f"{red_balls_str} + {blue_balls_str}"
