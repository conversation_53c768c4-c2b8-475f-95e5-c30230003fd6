#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试100期进度显示功能的脚本

专门测试每100期显示进度的功能
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.data_loader import DataLoader
from modules.statistical_analyzer import StatisticalAnalyzer
from modules.markov_chain import MarkovChainAnalyzer
from modules.prediction_engine import PredictionEngine
from modules.comparison_engine import ComparisonEngine
from modules.user_interface import UserInterface


def test_100_periods_progress():
    """测试100期进度显示功能"""
    print("=== 测试100期进度显示功能 ===")
    
    try:
        # 系统配置
        config = {
            'red_ball_range': (1, 33),
            'blue_ball_range': (1, 16),
            'red_ball_count': 6,
            'blue_ball_count': 1,
            'answer_periods': 6
        }
        
        # 初始化组件
        data_loader = DataLoader("lottery_data_all.xlsx")
        statistical_analyzer = StatisticalAnalyzer(config)
        markov_analyzer = MarkovChainAnalyzer(config)
        prediction_engine = PredictionEngine(config, statistical_analyzer, markov_analyzer)
        comparison_engine = ComparisonEngine(config)
        ui = UserInterface()
        
        # 设置参数（使用更早的期号以获得更多可分析期数）
        start_period = "25001"  # 从年初开始
        long_term_range = 500
        short_term_range = 50
        
        print(f"开始分析期号: {start_period}")
        print(f"远期数据范围: {long_term_range}期")
        print(f"近期数据范围: {short_term_range}期")
        
        # 获取可分析的期号
        analysis_periods = data_loader.get_analysis_periods(start_period)
        if not analysis_periods:
            print("没有可分析的期号！")
            return False
        
        print(f"原始可分析期数: {len(analysis_periods)}")
        
        # 限制到250期以测试100期和200期的进度显示
        test_periods = min(250, len(analysis_periods))
        analysis_periods = analysis_periods[:test_periods]
        
        # 显示分析开始信息
        ui.show_analysis_start_info(len(analysis_periods))
        
        results = []
        
        for i, period in enumerate(analysis_periods):
            # 获取当前数据库
            long_term_database = data_loader.get_database_for_period(period, long_term_range)
            short_term_database = data_loader.get_database_for_period(period, short_term_range)
            
            if (long_term_database is None or len(long_term_database) == 0 or
                short_term_database is None or len(short_term_database) == 0):
                continue
            
            # 获取答案数据
            answer_data = data_loader.get_answer_data(period)
            if answer_data is None or len(answer_data) == 0:
                continue
            
            # 运行预测
            latest_period = data_loader.get_latest_period(long_term_database)
            
            # 统计分析
            statistical_analyzer.analyze(long_term_database)
            long_term_stats = statistical_analyzer.get_probability_tables()
            
            statistical_analyzer.analyze(short_term_database)
            short_term_stats = statistical_analyzer.get_probability_tables()
            
            # 马尔科夫链分析
            markov_analyzer.analyze(long_term_database, latest_period)
            long_term_markov = markov_analyzer.get_probability_tables()
            
            markov_analyzer.analyze(short_term_database, latest_period)
            short_term_markov = markov_analyzer.get_probability_tables()
            
            # 生成预测
            predictions = prediction_engine.generate_predictions(
                long_term_stats, short_term_stats,
                long_term_markov, short_term_markov,
                latest_period
            )
            
            # 比对结果
            comparison_result = comparison_engine.compare_predictions(predictions, answer_data)
            
            results.append({
                'period': period,
                'predictions': predictions,
                'comparison': comparison_result,
                'long_term_database_size': len(long_term_database),
                'short_term_database_size': len(short_term_database),
                'latest_period': latest_period
            })
            
            # 每100期显示一次进度（这是我们要测试的核心功能）
            if (i + 1) % 100 == 0:
                print(f"\n*** 达到{i + 1}期里程碑 ***")
                ui.show_analysis_progress(
                    i + 1, len(analysis_periods),
                    len(long_term_database), len(short_term_database), latest_period
                )
            
            # 每10期显示一个简单进度点
            elif (i + 1) % 10 == 0:
                print(f"已完成 {i + 1} 期...", end=" ")
                if (i + 1) % 50 == 0:
                    print()  # 每50期换行
        
        print(f"\n\n分析完成！")
        
        # 显示最终统计结果
        ui.show_final_analysis_results(results)
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("双色球彩票预测与分析系统 - 100期进度显示功能测试")
    print("=" * 60)
    
    # 检查数据文件
    if not os.path.exists("lottery_data_all.xlsx"):
        print("错误: 数据文件 'lottery_data_all.xlsx' 不存在！")
        return
    
    # 运行测试
    result = test_100_periods_progress()
    
    print("\n" + "=" * 60)
    if result:
        print("100期进度显示功能测试通过！")
        print("✅ 分析开始信息显示正常")
        print("✅ 每100期进度显示正常")
        print("✅ 最终统计结果显示正常")
    else:
        print("100期进度显示功能测试失败！")


if __name__ == "__main__":
    main()
