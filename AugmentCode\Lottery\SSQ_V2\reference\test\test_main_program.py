#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序的分析比对逻辑
模拟用户运行主程序的完全相同场景
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def test_main_program_analysis():
    """测试主程序的分析比对逻辑"""
    print("=== 测试主程序的分析比对逻辑 ===")
    print("模拟用户运行主程序：从25001期开始，数据库范围99")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 用户的参数
    start_period = '25001'
    database_range = 99
    
    print(f"开始期号: {start_period}")
    print(f"数据库范围: {database_range}")
    
    # 获取分析期号列表
    analysis_periods = system.data_loader.get_analysis_periods(start_period)
    
    print(f"总分析期数: {len(analysis_periods)}")
    
    # 模拟主程序的分析比对循环，只运行到第50期
    results = []
    
    for i, period in enumerate(analysis_periods):
        if i >= 50:  # 只运行前50期
            break
            
        print(f"\n--- 第{i+1}期分析 (期号: {period}) ---")
        
        # 获取当前数据库（与主程序完全相同的逻辑）
        current_database = system.data_loader.get_database_for_period(
            period, database_range
        )
        
        if current_database is None or len(current_database) == 0:
            print(f"跳过期号 {period}：无数据")
            continue
        
        # 获取答案数据
        answer_data = system.data_loader.get_answer_data(period)
        
        if answer_data is None or len(answer_data) == 0:
            print(f"跳过期号 {period}：无答案数据")
            continue
        
        # 运行预测（与主程序完全相同的逻辑）
        latest_period = system.data_loader.get_latest_period(current_database)
        
        print(f"数据库最新期: {latest_period['period']}")
        
        # 分析
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = system.prediction_engine.generate_all_predictions(
            current_database, latest_period
        )
        
        # 比对结果
        comparison_result = system.comparison_engine.compare_predictions(
            predictions, answer_data
        )
        
        results.append({
            'period': period,
            'predictions': predictions,
            'comparison': comparison_result,
            'database_size': len(current_database),
            'latest_period': latest_period
        })
        
        # 第50期显示进度（与主程序完全相同的逻辑）
        if (i + 1) == 50:
            print(f"\n=== 第{i+1}期进度显示 ===")
            
            # 获取预测用的冷球信息（包含最新1期的最近5期）
            pred_red_cold_balls, pred_blue_cold_balls = system.statistical_analyzer.get_cold_balls(latest_period)

            # 获取分析用的冷球信息（最新1期之前的5期）
            analysis_red_cold_balls, analysis_blue_cold_balls = system.statistical_analyzer.get_cold_balls_for_analysis(latest_period, current_database)

            # 获取筛选要求
            filter_requirements = system.prediction_engine.filter_requirements

            # 计算最新1期的重号数（与上期比较）
            repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
            
            print(f"期号: {period}")
            print(f"数据库最新期: {latest_period['period']}")
            red_str = ' '.join(map(str, latest_period['red_balls']))
            print(f"最新期号码: {latest_period['period']} {red_str} + {latest_period['blue_ball']}")
            print(f"计算的重号数: {repeat_count}")
            
            # 如果是25050期，进行详细验证
            if latest_period['period'] == '25050':
                print(f"\n=== 详细验证25050期重号数 ===")
                
                # 手动验证
                current_period_num = int(latest_period['period'])
                prev_period_num = current_period_num - 1
                
                print(f"查找上一期: {prev_period_num}")
                
                prev_row = None
                for _, row in current_database.iterrows():
                    if row['NO'] == prev_period_num:
                        prev_row = row
                        break
                
                if prev_row is not None:
                    prev_red_balls = [int(prev_row[f'r{j}']) for j in range(1, 7)]
                    prev_blue_ball = int(prev_row['b'])
                    prev_red_str = ' '.join(map(str, prev_red_balls))
                    print(f"上一期号码: {prev_period_num} {prev_red_str} + {prev_blue_ball}")
                    
                    # 计算重号
                    current_red_balls = latest_period['red_balls']
                    repeat_balls = [ball for ball in current_red_balls if ball in prev_red_balls]
                    manual_repeat_count = len(repeat_balls)
                    
                    print(f"当前期红球: {current_red_balls}")
                    print(f"上一期红球: {prev_red_balls}")
                    print(f"重号球: {repeat_balls}")
                    print(f"手动计算重号数: {manual_repeat_count}")
                    print(f"系统计算重号数: {repeat_count}")
                    
                    if manual_repeat_count == repeat_count == 1:
                        print("✅ 重号数计算正确")
                    else:
                        print("❌ 重号数计算错误")
                        return False
                else:
                    print("❌ 未找到上一期数据")
                    return False

            # 显示进度（与主程序完全相同）
            system.ui.show_analysis_progress(
                i + 1, len(analysis_periods),
                len(current_database), latest_period,
                predictions, filter_requirements,
                (pred_red_cold_balls, pred_blue_cold_balls), repeat_count,
                (analysis_red_cold_balls, analysis_blue_cold_balls)
            )
            
            return repeat_count
    
    return None


def main():
    """主函数"""
    print("主程序分析比对逻辑测试")
    print("=" * 60)
    print("完全模拟用户运行主程序的场景")
    
    repeat_count = test_main_program_analysis()
    
    print("\n" + "=" * 60)
    if repeat_count == 1:
        print("🎉 主程序分析比对逻辑正确！")
        print("重号数计算结果：1")
        return True
    elif repeat_count == 0:
        print("⚠️ 主程序显示重号数为0")
        print("这可能表明主程序中仍有问题需要修正")
        return False
    else:
        print("❌ 测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
