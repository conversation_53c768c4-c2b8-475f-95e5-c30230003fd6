#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球彩票预测与分析系统 (SSQ Lottery Prediction and Analysis System)
主程序入口文件

作者: AI Assistant
版本: 1.1 (重号数修正版)
日期: 2025-07-19
"""

import sys
import os
from typing import Dict, List, Tuple, Optional
import pandas as pd
import numpy as np
from pathlib import Path

# 导入自定义模块
from modules.data_loader import DataLoader
from modules.statistical_analyzer import StatisticalAnalyzer
from modules.markov_chain import MarkovChainAnalyzer
from modules.bayesian_analyzer import BayesianAnalyzer
from modules.prediction_engine import PredictionEngine
from modules.comparison_engine import ComparisonEngine
from modules.export_manager import ExportManager
from modules.user_interface import UserInterface


class SSQLotterySystem:
    """
    双色球彩票预测与分析系统主类
    
    该类整合了所有功能模块，提供统一的接口来执行预测和分析任务
    """
    
    def __init__(self, data_file_path: str = "lottery_data_all.xlsx"):
        """
        初始化系统
        
        Args:
            data_file_path: 数据文件路径
        """
        self.data_file_path = data_file_path
        self.data_loader = None
        self.statistical_analyzer = None
        self.markov_analyzer = None
        self.bayesian_analyzer = None
        self.prediction_engine = None
        self.comparison_engine = None
        self.export_manager = None
        self.ui = UserInterface()
        
        # 系统配置
        self.config = {
            'red_ball_range': (1, 33),      # 红球号码范围
            'blue_ball_range': (1, 16),     # 蓝球号码范围
            'red_ball_count': 6,             # 每期红球数量
            'blue_ball_count': 1,            # 每期蓝球数量
            'red_big_ball_threshold': 16,    # 红球大球阈值
            'blue_big_ball_threshold': 8,    # 蓝球大球阈值
            'cold_ball_periods': 5,          # 冷球统计期数
            'prediction_groups': 24,         # 预测组数
            'answer_periods': 6              # 答案数据期数
        }
        
        self._initialize_system()
    
    def _initialize_system(self):
        """初始化系统各个模块"""
        try:
            # 初始化数据加载器
            self.data_loader = DataLoader(self.data_file_path)
            
            # 初始化分析器
            self.statistical_analyzer = StatisticalAnalyzer(self.config)
            self.markov_analyzer = MarkovChainAnalyzer(self.config)
            self.bayesian_analyzer = BayesianAnalyzer(self.config)
            
            # 初始化预测引擎
            self.prediction_engine = PredictionEngine(
                self.config,
                self.statistical_analyzer,
                self.markov_analyzer,
                self.bayesian_analyzer
            )
            
            # 初始化比对引擎
            self.comparison_engine = ComparisonEngine(self.config)
            
            # 初始化导出管理器
            self.export_manager = ExportManager()
            
            print("系统初始化完成！")
            
        except Exception as e:
            print(f"系统初始化失败: {e}")
            sys.exit(1)
    
    def run(self):
        """运行主程序"""
        try:
            self.ui.show_welcome()
            
            while True:
                choice = self.ui.get_main_menu_choice()
                
                if choice == 1:
                    self._run_prediction_mode()
                elif choice == 2:
                    self._run_analysis_mode()
                elif choice == 0:
                    print("感谢使用双色球预测分析系统！")
                    break
                else:
                    print("无效选择，请重新输入。")
                    
        except KeyboardInterrupt:
            print("\n程序被用户中断。")
        except Exception as e:
            print(f"程序运行出错: {e}")
    
    def _run_prediction_mode(self):
        """运行预测选号模式"""
        print("\n=== 预测选号模式 ===")
        
        # 获取用户输入的数据库范围
        database_range = self.ui.get_database_range()
        
        # 加载和处理数据
        current_database = self.data_loader.get_current_database(database_range)
        
        if current_database is None or len(current_database) == 0:
            print("数据加载失败或数据为空！")
            return
        
        # 获取最新一期信息
        latest_period = self.data_loader.get_latest_period(current_database)
        self.ui.show_latest_period_info(latest_period, current_database)
        
        # 运行统计分析
        self.statistical_analyzer.analyze(current_database)

        # 获取并显示冷球信息
        red_cold_balls, blue_cold_balls = self.statistical_analyzer.get_cold_balls(latest_period)
        self.ui.show_cold_ball_info(red_cold_balls, blue_cold_balls)

        # 运行马尔科夫链分析
        self.markov_analyzer.analyze(current_database, latest_period)
        
        # 运行贝叶斯分析
        self.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = self.prediction_engine.generate_all_predictions(
            current_database, latest_period
        )

        # 获取筛选要求信息
        filter_requirements = self.prediction_engine.filter_requirements

        # 显示预测结果
        self.ui.show_prediction_results(predictions, filter_requirements, (red_cold_balls, blue_cold_balls), latest_period)
        
        # 询问是否保存结果
        if self.ui.ask_save_results():
            self._save_prediction_results(predictions, current_database)
    
    def _run_analysis_mode(self):
        """运行分析比对模式"""
        print("\n=== 分析比对模式 ===")
        
        # 获取目标期号
        target_period = self.ui.get_target_period()
        
        # 获取数据库范围
        database_range = self.ui.get_database_range()
        
        # 运行分析比对
        self._run_comparison_analysis(target_period, database_range)
    
    def _run_comparison_analysis(self, start_period: str, database_range: int):
        """
        运行比对分析
        
        Args:
            start_period: 开始期号
            database_range: 数据库范围
        """
        try:
            # 获取所有可分析的期号
            analysis_periods = self.data_loader.get_analysis_periods(start_period)
            
            if not analysis_periods:
                print("没有可分析的期号！")
                return
            
            print(f"需要分析比对的总期数: {len(analysis_periods)}")
            
            results = []
            
            for i, period in enumerate(analysis_periods):
                # 获取当前数据库
                current_database = self.data_loader.get_database_for_period(
                    period, database_range
                )
                
                if current_database is None or len(current_database) == 0:
                    continue
                
                # 获取答案数据
                answer_data = self.data_loader.get_answer_data(period)
                
                if answer_data is None or len(answer_data) == 0:
                    continue
                
                # 运行预测
                latest_period = self.data_loader.get_latest_period(current_database)
                
                # 分析
                self.statistical_analyzer.analyze(current_database)
                self.markov_analyzer.analyze(current_database, latest_period)
                self.bayesian_analyzer.analyze(current_database, latest_period)
                
                # 生成预测
                predictions = self.prediction_engine.generate_all_predictions(
                    current_database, latest_period
                )
                
                # 比对结果
                comparison_result = self.comparison_engine.compare_predictions(
                    predictions, answer_data
                )
                
                results.append({
                    'period': period,
                    'predictions': predictions,
                    'comparison': comparison_result,
                    'database_size': len(current_database),
                    'latest_period': latest_period
                })
                
                # 每50期显示一次进度
                if (i + 1) % 50 == 0:
                    # 获取预测用的冷球信息（包含最新1期的最近5期）
                    pred_red_cold_balls, pred_blue_cold_balls = self.statistical_analyzer.get_cold_balls(latest_period)

                    # 获取分析用的冷球信息（最新1期之前的5期）
                    analysis_red_cold_balls, analysis_blue_cold_balls = self.statistical_analyzer.get_cold_balls_for_analysis(latest_period, current_database)

                    # 获取筛选要求
                    filter_requirements = self.prediction_engine.filter_requirements

                    # 计算最新1期的重号数（与上期比较）
                    repeat_count = self._calculate_repeat_count_for_analysis(current_database, latest_period)

                    # 调试信息：验证重号数计算
                    if latest_period['period'] == '25050':
                        print(f"[调试] 25050期重号数计算: {repeat_count}")
                        # 手动验证
                        prev_period_num = 25049
                        prev_row = None
                        for _, row in current_database.iterrows():
                            if row['NO'] == prev_period_num:
                                prev_row = row
                                break
                        if prev_row is not None:
                            prev_red_balls = [int(prev_row[f'r{j}']) for j in range(1, 7)]
                            current_red_balls = latest_period['red_balls']
                            repeat_balls = [ball for ball in current_red_balls if ball in prev_red_balls]
                            print(f"[调试] 当前期红球: {current_red_balls}")
                            print(f"[调试] 上一期红球: {prev_red_balls}")
                            print(f"[调试] 重号球: {repeat_balls}")
                            print(f"[调试] 手动计算重号数: {len(repeat_balls)}")
                        else:
                            print(f"[调试] 未找到上一期 {prev_period_num}")
                            print(f"[调试] 数据库期号范围: {current_database['NO'].min()} - {current_database['NO'].max()}")

                    self.ui.show_analysis_progress(
                        i + 1, len(analysis_periods),
                        len(current_database), latest_period,
                        predictions, filter_requirements,
                        (pred_red_cold_balls, pred_blue_cold_balls), repeat_count,
                        (analysis_red_cold_balls, analysis_blue_cold_balls)
                    )
            
            # 显示最终统计结果
            self.ui.show_final_analysis_results(results)

            # 打印高命中数详细信息
            self._print_high_hit_details(results)

            # 保存结果
            self._save_analysis_results(results)
            
        except Exception as e:
            print(f"分析比对过程中出错: {e}")
    
    def _save_prediction_results(self, predictions: Dict, database: pd.DataFrame):
        """保存预测结果"""
        try:
            # 获取所有概率表格
            probability_tables = {
                'statistical': self.statistical_analyzer.get_probability_tables(),
                'markov': self.markov_analyzer.get_probability_tables(),
                'bayesian': self.bayesian_analyzer.get_probability_tables()
            }
            
            # 导出到Excel
            self.export_manager.export_prediction_results(
                predictions, probability_tables, database
            )
            
            print("预测结果已保存到Excel文件！")
            
        except Exception as e:
            print(f"保存预测结果失败: {e}")
    
    def _calculate_repeat_count(self, database, latest_period: Dict) -> int:
        """计算最新1期的重号数（与上期比较）"""
        try:
            # 找到最新期在数据库中的位置
            latest_period_num = int(latest_period['period'])
            latest_index = None

            for i, row in database.iterrows():
                if row['NO'] == latest_period_num:
                    latest_index = i
                    break

            if latest_index is None or latest_index == 0:
                return 0

            # 获取上一期的红球号码
            prev_row = database.iloc[latest_index - 1]
            prev_red_balls = [int(prev_row[f'r{j}']) for j in range(1, 7)]

            # 计算重号数
            current_red_balls = latest_period['red_balls']
            repeat_count = sum(1 for ball in current_red_balls if ball in prev_red_balls)

            return repeat_count

        except Exception:
            return 0

    def _calculate_repeat_count_for_analysis(self, database, current_period: Dict) -> int:
        """计算分析比对模式中的重号数（基于实际期号）"""
        try:
            current_period_num = int(current_period['period'])
            prev_period_num = current_period_num - 1

            # 在数据库中查找上一期的数据
            prev_row = None
            for _, row in database.iterrows():
                if row['NO'] == prev_period_num:
                    prev_row = row
                    break

            if prev_row is None:
                return 0

            # 获取上一期的红球号码
            prev_red_balls = [int(prev_row[f'r{j}']) for j in range(1, 7)]

            # 计算重号数
            current_red_balls = current_period['red_balls']
            repeat_count = sum(1 for ball in current_red_balls if ball in prev_red_balls)

            return repeat_count

        except Exception:
            return 0

    def _save_analysis_results(self, results: List[Dict]):
        """保存分析结果"""
        try:
            self.export_manager.export_analysis_results(results)
            print("分析结果已保存到Excel文件！")

        except Exception as e:
            print(f"保存分析结果失败: {e}")

    def _print_high_hit_details(self, results):
        """
        打印高命中数（6球和7球）的详细信息

        Args:
            results: 分析结果列表
        """
        print("\n" + "=" * 80)
        print("🎯 高命中数详细信息 (6球和7球)")
        print("=" * 80)

        high_hit_found = False

        for result in results:
            period = result['period']
            predictions = result['predictions']
            comparison = result['comparison']

            # 检查每个预测组
            for group_id in range(1, 25):
                if group_id in predictions and group_id in comparison:
                    group_result = comparison[group_id]
                    prediction = group_result['prediction']  # 使用比对结果中的预测数据，避免引用问题
                    max_hit = group_result['max_hit']

                    # 只显示6球和7球的命中
                    if max_hit['total_hits'] >= 6:
                        high_hit_found = True

                        # 格式化预测红球
                        red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])

                        # 格式化答案红球
                        answer_red_str = ' '.join(f"{ball:2d}" for ball in max_hit['answer_red_balls'])

                        print(f"\n🔥 【{max_hit['total_hits']}球命中】")
                        print(f"   分析期号: {period}")
                        print(f"   预测组号: 第{group_id}组")
                        print(f"   预测方法: {prediction['method']}")
                        print(f"   预测红球: {red_balls_str}")
                        print(f"   预测蓝球: {prediction['blue_ball']:2d}")
                        print(f"   最大命中数: {max_hit['total_hits']}球")
                        print(f"   最大命中期号: {max_hit['period']}")
                        print(f"   命中期号红球: {answer_red_str}")
                        print(f"   命中期号蓝球: {max_hit['answer_blue_ball']:2d}")
                        print(f"   红球命中数: {max_hit['red_hits']}个")
                        print(f"   蓝球命中数: {max_hit['blue_hits']}个")
                        print(f"   蓝球命中状态: {'是' if max_hit['blue_hits'] == 1 else '否'}")

                        # 显示具体命中的红球
                        pred_red_set = set(prediction['red_balls'])
                        answer_red_set = set(max_hit['answer_red_balls'])
                        hit_red_balls = sorted(list(pred_red_set & answer_red_set))
                        hit_red_str = ' '.join(f"{ball:2d}" for ball in hit_red_balls)
                        print(f"   命中红球详情: {hit_red_str}")

                        print("-" * 60)

        if not high_hit_found:
            print("\n   暂无6球或7球的高命中记录")
            print("-" * 60)

        print("=" * 80)


def main():
    """主函数"""
    try:
        # 检查数据文件是否存在
        data_file = "lottery_data_all.xlsx"
        if not os.path.exists(data_file):
            print(f"错误: 数据文件 '{data_file}' 不存在！")
            print("请确保数据文件在程序根目录下。")
            sys.exit(1)
        
        # 创建并运行系统
        system = SSQLotterySystem(data_file)
        system.run()
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
