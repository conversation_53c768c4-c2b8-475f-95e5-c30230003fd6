#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的功能
验证冷球计算和预测结果详细信息显示
"""

import sys
from modules.data_loader import DataLoader
from modules.statistical_analyzer import StatisticalAnalyzer
from modules.markov_chain import MarkovChainAnalyzer
from modules.bayesian_analyzer import BayesianAnalyzer
from modules.prediction_engine import PredictionEngine
from modules.user_interface import UserInterface


def test_cold_ball_calculation():
    """测试冷球计算修正"""
    print("=== 测试冷球计算修正 ===")
    
    config = {
        'red_ball_range': (1, 33),
        'blue_ball_range': (1, 16),
        'red_big_ball_threshold': 16,
        'blue_big_ball_threshold': 8,
        'cold_ball_periods': 5
    }
    
    loader = DataLoader('lottery_data_all.xlsx')
    database = loader.get_current_database(0)  # 全部数据
    latest_period = loader.get_latest_period(database)
    
    print(f"数据库总期数: {len(database)}")
    print(f"数据范围: {min(database['NO'])} - {max(database['NO'])}")
    
    # 手动检查最近5期
    recent_5_periods = database.tail(5)
    print("\n最近5期数据:")
    for i, (_, row) in enumerate(recent_5_periods.iterrows()):
        red_balls = [int(row[f'r{j}']) for j in range(1, 7)]
        blue_ball = int(row['b'])
        red_str = ' '.join(map(str, red_balls))
        period = row['NO']
        print(f"{period} {red_str} + {blue_ball}")
    
    # 手动计算最近5期出现的号码
    recent_red = set()
    recent_blue = set()
    for _, row in recent_5_periods.iterrows():
        for j in range(1, 7):
            recent_red.add(int(row[f'r{j}']))
        recent_blue.add(int(row['b']))
    
    print(f"\n最近5期出现的红球: {sorted(recent_red)}")
    print(f"最近5期出现的蓝球: {sorted(recent_blue)}")
    
    # 计算冷球
    all_red = set(range(1, 34))
    all_blue = set(range(1, 17))
    expected_red_cold = sorted(all_red - recent_red)
    expected_blue_cold = sorted(all_blue - recent_blue)
    
    print(f"\n期望的红球冷球: {expected_red_cold}")
    print(f"期望的蓝球冷球: {expected_blue_cold}")
    
    # 使用修正后的函数计算
    analyzer = StatisticalAnalyzer(config)
    analyzer.analyze(database)
    red_cold, blue_cold = analyzer.get_cold_balls(latest_period)
    
    print(f"\n函数计算的红球冷球: {red_cold}")
    print(f"函数计算的蓝球冷球: {blue_cold}")
    
    # 验证结果
    if red_cold == expected_red_cold and blue_cold == expected_blue_cold:
        print("✅ 冷球计算修正成功！")
        return True
    else:
        print("❌ 冷球计算仍有问题")
        return False


def test_prediction_details():
    """测试预测结果详细信息显示"""
    print("\n=== 测试预测结果详细信息显示 ===")
    
    config = {
        'red_ball_range': (1, 33),
        'blue_ball_range': (1, 16),
        'red_ball_count': 6,
        'blue_ball_count': 1,
        'red_big_ball_threshold': 16,
        'blue_big_ball_threshold': 8,
        'cold_ball_periods': 5,
        'prediction_groups': 24,
        'answer_periods': 6
    }
    
    loader = DataLoader('lottery_data_all.xlsx')
    database = loader.get_current_database(0)
    latest_period = loader.get_latest_period(database)
    
    # 运行分析
    stat_analyzer = StatisticalAnalyzer(config)
    markov_analyzer = MarkovChainAnalyzer(config)
    bayesian_analyzer = BayesianAnalyzer(config)
    
    stat_analyzer.analyze(database)
    markov_analyzer.analyze(database, latest_period)
    bayesian_analyzer.analyze(database, latest_period)
    
    # 生成预测
    prediction_engine = PredictionEngine(config, stat_analyzer, markov_analyzer, bayesian_analyzer)
    predictions = prediction_engine.generate_all_predictions(database, latest_period)
    
    # 获取筛选要求
    filter_requirements = prediction_engine.filter_requirements
    print(f"筛选要求: {filter_requirements}")
    
    # 获取冷球信息
    red_cold_balls, blue_cold_balls = stat_analyzer.get_cold_balls(latest_period)
    
    # 测试第4、5、6组的详细信息显示
    ui = UserInterface()
    
    for group_id in [4, 5, 6]:
        prediction = predictions[group_id]
        print(f"\n第{group_id}组: {prediction['method']}")
        red_str = ' '.join(map(str, prediction['red_balls']))
        print(f"预测号码: {red_str} + {prediction['blue_ball']}")
        ui._show_prediction_details(group_id, prediction, filter_requirements, (red_cold_balls, blue_cold_balls), latest_period)
    
    print("\n✅ 预测结果详细信息显示测试完成！")
    return True


def main():
    """主测试函数"""
    print("双色球系统修正功能测试")
    print("=" * 50)
    
    success1 = test_cold_ball_calculation()
    success2 = test_prediction_details()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 所有修正功能测试通过！")
        return True
    else:
        print("⚠️ 部分功能仍需修正")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
