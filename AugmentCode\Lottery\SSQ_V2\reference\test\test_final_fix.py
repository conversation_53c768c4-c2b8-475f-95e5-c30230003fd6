# -*- coding: utf-8 -*-
"""
测试最终修复效果
验证所有修复是否解决了用户反馈的问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ssq_lottery_system import SSQLotterySystem
import copy


def test_final_fix():
    """测试最终修复效果"""
    print("=== 测试最终修复效果 ===")
    
    try:
        # 创建系统实例
        system = SSQLotterySystem("lottery_data_all.xlsx")
        
        # 模拟用户运行主程序的完整流程
        target_period = "25067"
        database_range = 99
        
        print(f"分析期号: {target_period}")
        print(f"数据库范围: {database_range}")
        
        # 获取当前数据库
        current_database = system.data_loader.get_database_for_period(
            target_period, database_range
        )
        
        # 获取答案数据
        answer_data = system.data_loader.get_answer_data(target_period)
        
        # 运行预测
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 分析
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = system.prediction_engine.generate_all_predictions(
            current_database, latest_period
        )
        
        # 比对结果
        comparison_result = system.comparison_engine.compare_predictions(
            predictions, answer_data
        )
        
        # 模拟主程序的results存储
        results = []
        results.append({
            'period': target_period,
            'predictions': predictions,
            'comparison': comparison_result,
            'database_size': len(current_database),
            'latest_period': latest_period
        })
        
        print(f"\n=== 验证修复前后的差异 ===")
        
        # 检查第9组的数据
        result = results[0]
        predictions_from_result = result['predictions']
        comparison_from_result = result['comparison']
        
        # 修复前的逻辑（从results['predictions']获取）
        prediction_old_way = predictions_from_result[9]
        
        # 修复后的逻辑（从results['comparison'][group_id]['prediction']获取）
        prediction_new_way = comparison_from_result[9]['prediction']
        
        print(f"修复前逻辑（从results['predictions']获取）:")
        print(f"  红球: {prediction_old_way['red_balls']}")
        print(f"  蓝球: {prediction_old_way['blue_ball']}")
        print(f"  方法: {prediction_old_way['method']}")
        
        print(f"修复后逻辑（从results['comparison']['prediction']获取）:")
        print(f"  红球: {prediction_new_way['red_balls']}")
        print(f"  蓝球: {prediction_new_way['blue_ball']}")
        print(f"  方法: {prediction_new_way['method']}")
        
        print(f"两种方式是否相同: {prediction_old_way == prediction_new_way}")
        print(f"是否相同对象: {prediction_old_way is prediction_new_way}")
        
        # 模拟修改原始预测数据，看是否影响显示
        print(f"\n=== 模拟数据修改测试 ===")
        
        # 保存原始值
        original_red_0 = prediction_old_way['red_balls'][0]
        original_blue = prediction_old_way['blue_ball']
        
        print(f"修改前:")
        print(f"  predictions[9]红球[0]: {prediction_old_way['red_balls'][0]}")
        print(f"  predictions[9]蓝球: {prediction_old_way['blue_ball']}")
        print(f"  comparison[9]['prediction']红球[0]: {prediction_new_way['red_balls'][0]}")
        print(f"  comparison[9]['prediction']蓝球: {prediction_new_way['blue_ball']}")
        
        # 修改原始预测数据
        prediction_old_way['red_balls'][0] = 99
        prediction_old_way['blue_ball'] = 99
        
        print(f"修改后:")
        print(f"  predictions[9]红球[0]: {prediction_old_way['red_balls'][0]}")
        print(f"  predictions[9]蓝球: {prediction_old_way['blue_ball']}")
        print(f"  comparison[9]['prediction']红球[0]: {prediction_new_way['red_balls'][0]}")
        print(f"  comparison[9]['prediction']蓝球: {prediction_new_way['blue_ball']}")
        
        # 检查修复效果
        if (prediction_old_way['red_balls'][0] != prediction_new_way['red_balls'][0] or
            prediction_old_way['blue_ball'] != prediction_new_way['blue_ball']):
            print("✅ 修复成功！修改原始数据不影响比对结果中的数据")
        else:
            print("❌ 修复失败！仍然存在引用问题")
        
        # 恢复原始值
        prediction_old_way['red_balls'][0] = original_red_0
        prediction_old_way['blue_ball'] = original_blue
        
        # 调用修复后的_print_high_hit_details方法
        print(f"\n=== 调用修复后的打印方法 ===")
        system._print_high_hit_details(results)
        
        # 验证比对结果的正确性
        print(f"\n=== 验证比对结果正确性 ===")
        max_hit = comparison_result[9]['max_hit']
        prediction = comparison_result[9]['prediction']
        
        # 找到最大命中期号的答案数据
        target_answer = None
        for answer in answer_data:
            if answer['period'] == max_hit['period']:
                target_answer = answer
                break
        
        if target_answer:
            # 手动验证比对结果
            pred_red_set = set([int(x) for x in prediction['red_balls']])
            answer_red_set = set(target_answer['red_balls'])
            hit_red_balls = pred_red_set & answer_red_set
            manual_red_hits = len(hit_red_balls)
            
            manual_blue_hits = 1 if int(prediction['blue_ball']) == target_answer['blue_ball'] else 0
            manual_total_hits = manual_red_hits + manual_blue_hits
            
            print(f"期号 {max_hit['period']} 手动验证:")
            print(f"  预测红球: {sorted([int(x) for x in prediction['red_balls']])}")
            print(f"  答案红球: {sorted(target_answer['red_balls'])}")
            print(f"  命中红球: {sorted(list(hit_red_balls))}")
            print(f"  预测蓝球: {int(prediction['blue_ball'])}")
            print(f"  答案蓝球: {target_answer['blue_ball']}")
            print(f"  手动计算: 红{manual_red_hits}+蓝{manual_blue_hits}={manual_total_hits}")
            print(f"  引擎计算: 红{max_hit['red_hits']}+蓝{max_hit['blue_hits']}={max_hit['total_hits']}")
            
            if (manual_red_hits == max_hit['red_hits'] and 
                manual_blue_hits == max_hit['blue_hits'] and 
                manual_total_hits == max_hit['total_hits']):
                print("✅ 比对算法完全正确")
                
                # 特别检查蓝球命中逻辑
                if manual_blue_hits == 1 and max_hit['blue_hit_status'] == True:
                    print("✅ 蓝球命中逻辑正确")
                elif manual_blue_hits == 0 and max_hit['blue_hit_status'] == False:
                    print("✅ 蓝球未命中逻辑正确")
                else:
                    print("❌ 蓝球命中状态逻辑错误")
            else:
                print("❌ 比对算法存在错误")
        
        print(f"\n✅ 所有修复已完成并验证！")
        return True
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_final_fix()
