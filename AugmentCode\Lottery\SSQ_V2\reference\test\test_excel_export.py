#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel导出功能
验证是否包含所有必需的表格
"""

import pandas as pd
import os
import sys


def test_excel_export():
    """测试Excel导出功能"""
    print("=== 测试Excel导出功能 ===")
    
    # 查找最新的Excel文件
    output_dir = "output"
    excel_files = [f for f in os.listdir(output_dir) if f.endswith('.xlsx')]
    
    if not excel_files:
        print("❌ 没有找到Excel文件")
        return False
    
    # 使用最新的文件
    latest_file = max(excel_files)
    file_path = os.path.join(output_dir, latest_file)
    
    print(f"检查文件: {latest_file}")
    
    try:
        # 读取Excel文件的所有工作表名称
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        
        print(f"发现 {len(sheet_names)} 个工作表:")
        for i, sheet_name in enumerate(sheet_names, 1):
            print(f"  {i}. {sheet_name}")
        
        # 检查必需的工作表
        required_sheets = [
            '预测结果',
            '红球历史概率',
            '蓝球历史概率',
            '红球大球数概率',
            '蓝球大球数概率',
            '红球冷球数概率',
            '蓝球冷球数概率',  # 新添加
            '红球重号数概率',
            '红球马尔科夫概率',
            '蓝球马尔科夫概率',
            '红球贝叶斯概率',
            '蓝球贝叶斯概率',
            '红球跟随性概率矩阵',  # 新添加
            '蓝球跟随性概率矩阵',  # 新添加
            '数据库信息'
        ]
        
        print(f"\n检查必需的工作表:")
        missing_sheets = []
        for sheet in required_sheets:
            if sheet in sheet_names:
                print(f"  ✅ {sheet}")
            else:
                print(f"  ❌ {sheet} (缺失)")
                missing_sheets.append(sheet)
        
        if missing_sheets:
            print(f"\n❌ 缺失 {len(missing_sheets)} 个工作表: {missing_sheets}")
            return False
        
        # 检查新添加的工作表内容
        print(f"\n检查新添加的工作表内容:")
        
        # 检查蓝球冷球数概率
        try:
            blue_cold_df = pd.read_excel(file_path, sheet_name='蓝球冷球数概率')
            print(f"  ✅ 蓝球冷球数概率: {len(blue_cold_df)} 行数据")
            print(f"      列名: {list(blue_cold_df.columns)}")
        except Exception as e:
            print(f"  ❌ 蓝球冷球数概率读取失败: {e}")
        
        # 检查红球跟随性概率矩阵
        try:
            red_follow_df = pd.read_excel(file_path, sheet_name='红球跟随性概率矩阵')
            print(f"  ✅ 红球跟随性概率矩阵: {red_follow_df.shape} 形状")
            print(f"      列数: {len(red_follow_df.columns)}")
        except Exception as e:
            print(f"  ❌ 红球跟随性概率矩阵读取失败: {e}")
        
        # 检查蓝球跟随性概率矩阵
        try:
            blue_follow_df = pd.read_excel(file_path, sheet_name='蓝球跟随性概率矩阵')
            print(f"  ✅ 蓝球跟随性概率矩阵: {blue_follow_df.shape} 形状")
            print(f"      列数: {len(blue_follow_df.columns)}")
        except Exception as e:
            print(f"  ❌ 蓝球跟随性概率矩阵读取失败: {e}")
        
        print(f"\n✅ Excel导出功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ Excel文件读取失败: {e}")
        return False


def main():
    """主测试函数"""
    print("Excel导出功能测试")
    print("=" * 50)
    
    success = test_excel_export()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Excel导出功能测试通过！")
        return True
    else:
        print("⚠️ Excel导出功能需要修正")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
