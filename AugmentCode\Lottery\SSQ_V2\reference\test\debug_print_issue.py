#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试打印功能的具体问题
检查为什么预测蓝球11显示为14
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def debug_print_issue():
    """调试打印功能的具体问题"""
    print("=== 调试打印功能的具体问题 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    try:
        # 分析25067期
        period = '25067'
        database_range = 99
        
        # 获取数据库和答案
        current_database = system.data_loader.get_database_for_period(period, database_range)
        answer_data = system.data_loader.get_answer_data(period)
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 分析
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
        
        # 比对结果
        comparison_result = system.comparison_engine.compare_predictions(predictions, answer_data)
        
        # 查找所有6球以上命中的组
        print("查找所有6球以上命中的组:")
        high_hit_groups = []
        
        for group_id in range(1, 25):
            if group_id in predictions and group_id in comparison_result:
                prediction = predictions[group_id]
                group_result = comparison_result[group_id]
                max_hit = group_result['max_hit']
                
                if max_hit['total_hits'] >= 6:
                    high_hit_groups.append((group_id, prediction, max_hit))
                    
                    print(f"\n第{group_id}组 - {max_hit['total_hits']}球命中:")
                    print(f"  prediction对象类型: {type(prediction)}")
                    print(f"  prediction内容: {prediction}")
                    print(f"  预测红球类型: {[type(x) for x in prediction['red_balls']]}")
                    print(f"  预测蓝球类型: {type(prediction['blue_ball'])}")
                    print(f"  预测蓝球值: {prediction['blue_ball']}")
                    print(f"  max_hit对象类型: {type(max_hit)}")
                    print(f"  max_hit内容: {max_hit}")
        
        if not high_hit_groups:
            print("没有找到6球以上命中的组")
            return False
        
        # 模拟打印功能的逻辑
        print(f"\n=== 模拟打印功能的逻辑 ===")
        
        for group_id, prediction, max_hit in high_hit_groups:
            print(f"\n--- 第{group_id}组 ---")
            
            # 检查每个步骤的数据
            print(f"步骤1 - 原始数据:")
            print(f"  prediction['red_balls']: {prediction['red_balls']}")
            print(f"  prediction['blue_ball']: {prediction['blue_ball']} (类型: {type(prediction['blue_ball'])})")
            print(f"  max_hit['answer_blue_ball']: {max_hit['answer_blue_ball']} (类型: {type(max_hit['answer_blue_ball'])})")
            
            # 格式化红球
            red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
            print(f"步骤2 - 格式化红球: {red_balls_str}")
            
            # 格式化答案红球
            answer_red_str = ' '.join(f"{ball:2d}" for ball in max_hit['answer_red_balls'])
            print(f"步骤3 - 格式化答案红球: {answer_red_str}")
            
            # 检查蓝球格式化
            try:
                pred_blue_formatted = f"{prediction['blue_ball']:2d}"
                print(f"步骤4 - 格式化预测蓝球: '{pred_blue_formatted}'")
            except Exception as e:
                print(f"步骤4 - 格式化预测蓝球出错: {e}")
                print(f"  尝试转换为int: {int(prediction['blue_ball'])}")
                pred_blue_formatted = f"{int(prediction['blue_ball']):2d}"
                print(f"  转换后格式化: '{pred_blue_formatted}'")
            
            try:
                answer_blue_formatted = f"{max_hit['answer_blue_ball']:2d}"
                print(f"步骤5 - 格式化答案蓝球: '{answer_blue_formatted}'")
            except Exception as e:
                print(f"步骤5 - 格式化答案蓝球出错: {e}")
                print(f"  尝试转换为int: {int(max_hit['answer_blue_ball'])}")
                answer_blue_formatted = f"{int(max_hit['answer_blue_ball']):2d}"
                print(f"  转换后格式化: '{answer_blue_formatted}'")
            
            # 模拟完整的打印输出
            print(f"\n模拟打印输出:")
            print(f"🔥 【{max_hit['total_hits']}球命中】")
            print(f"   分析期号: {period}")
            print(f"   预测组号: 第{group_id}组")
            print(f"   预测方法: {prediction['method']}")
            print(f"   预测红球: {red_balls_str}")
            print(f"   预测蓝球: {prediction['blue_ball']:2d}")
            print(f"   最大命中数: {max_hit['total_hits']}球")
            print(f"   最大命中期号: {max_hit['period']}")
            print(f"   命中期号红球: {answer_red_str}")
            print(f"   命中期号蓝球: {max_hit['answer_blue_ball']:2d}")
            print(f"   红球命中数: {max_hit['red_hits']}个")
            print(f"   蓝球命中数: {max_hit['blue_hits']}个")
            print(f"   蓝球命中状态: {'是' if max_hit['blue_hits'] == 1 else '否'}")
            
            # 显示具体命中的红球
            pred_red_set = set(prediction['red_balls'])
            answer_red_set = set(max_hit['answer_red_balls'])
            hit_red_balls = sorted(list(pred_red_set & answer_red_set))
            hit_red_str = ' '.join(f"{ball:2d}" for ball in hit_red_balls)
            print(f"   命中红球详情: {hit_red_str}")
            
            # 手动验证蓝球命中
            pred_blue = int(prediction['blue_ball'])
            answer_blue = int(max_hit['answer_blue_ball'])
            manual_blue_hits = 1 if pred_blue == answer_blue else 0
            
            print(f"\n手动验证:")
            print(f"  预测蓝球: {pred_blue}")
            print(f"  答案蓝球: {answer_blue}")
            print(f"  手动蓝球命中: {manual_blue_hits}")
            print(f"  比对引擎蓝球命中: {max_hit['blue_hits']}")
            
            if manual_blue_hits != max_hit['blue_hits']:
                print(f"  ❌ 蓝球命中数不一致！")
            else:
                print(f"  ✅ 蓝球命中数一致")
        
        return True
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("调试打印功能的具体问题")
    print("=" * 60)
    
    success = debug_print_issue()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 调试完成")
        print("请检查上面的输出，找出预测蓝球11显示为14的原因")
    else:
        print("❌ 调试失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
