#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析比对进度显示功能的脚本

测试新的分析比对进度显示和统计功能
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.data_loader import DataLoader
from modules.statistical_analyzer import StatisticalAnalyzer
from modules.markov_chain import MarkovChainAnalyzer
from modules.prediction_engine import PredictionEngine
from modules.comparison_engine import ComparisonEngine
from modules.user_interface import UserInterface


def test_analysis_progress_display():
    """测试分析比对进度显示功能"""
    print("=== 测试分析比对进度显示功能 ===")
    
    try:
        # 系统配置
        config = {
            'red_ball_range': (1, 33),
            'blue_ball_range': (1, 16),
            'red_ball_count': 6,
            'blue_ball_count': 1,
            'answer_periods': 6
        }
        
        # 初始化组件
        data_loader = DataLoader("lottery_data_all.xlsx")
        statistical_analyzer = StatisticalAnalyzer(config)
        markov_analyzer = MarkovChainAnalyzer(config)
        prediction_engine = PredictionEngine(config, statistical_analyzer, markov_analyzer)
        comparison_engine = ComparisonEngine(config)
        ui = UserInterface()
        
        # 设置参数（分析较多期数以测试进度显示）
        start_period = "25060"  # 使用更早的期号确保有足够的后续数据
        long_term_range = 500
        short_term_range = 50
        
        print(f"开始分析期号: {start_period}")
        print(f"远期数据范围: {long_term_range}期")
        print(f"近期数据范围: {short_term_range}期")
        
        # 获取可分析的期号
        analysis_periods = data_loader.get_analysis_periods(start_period)
        if not analysis_periods:
            print("没有可分析的期号！")
            return False
        
        # 限制分析期数（测试用，选择能被100整除的数量）
        test_periods = min(150, len(analysis_periods))  # 测试150期，可以看到100期进度显示
        analysis_periods = analysis_periods[:test_periods]
        
        # 显示分析开始信息
        ui.show_analysis_start_info(len(analysis_periods))
        
        results = []
        
        for i, period in enumerate(analysis_periods):
            print(f"\n正在分析第 {i+1}/{len(analysis_periods)} 期: {period}")
            
            # 获取当前数据库
            long_term_database = data_loader.get_database_for_period(period, long_term_range)
            short_term_database = data_loader.get_database_for_period(period, short_term_range)
            
            if (long_term_database is None or len(long_term_database) == 0 or
                short_term_database is None or len(short_term_database) == 0):
                continue
            
            # 获取答案数据
            answer_data = data_loader.get_answer_data(period)
            if answer_data is None or len(answer_data) == 0:
                continue
            
            # 运行预测
            latest_period = data_loader.get_latest_period(long_term_database)
            
            # 统计分析
            statistical_analyzer.analyze(long_term_database)
            long_term_stats = statistical_analyzer.get_probability_tables()
            
            statistical_analyzer.analyze(short_term_database)
            short_term_stats = statistical_analyzer.get_probability_tables()
            
            # 马尔科夫链分析
            markov_analyzer.analyze(long_term_database, latest_period)
            long_term_markov = markov_analyzer.get_probability_tables()
            
            markov_analyzer.analyze(short_term_database, latest_period)
            short_term_markov = markov_analyzer.get_probability_tables()
            
            # 生成预测
            predictions = prediction_engine.generate_predictions(
                long_term_stats, short_term_stats,
                long_term_markov, short_term_markov,
                latest_period
            )
            
            # 比对结果
            comparison_result = comparison_engine.compare_predictions(predictions, answer_data)
            
            # 显示简要结果
            for group_id, result in comparison_result.items():
                prediction = result['prediction']
                max_hit = result['max_hit_details']
                
                red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
                blue_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['blue_balls'])
                
                hit_info = ""
                if max_hit:
                    hit_info = f"最大命中: {max_hit['total_hits']}球 (期号: {max_hit['period']})"
                
                print(f"  第{group_id}组: {red_balls_str} + {blue_balls_str} {hit_info}")
            
            results.append({
                'period': period,
                'predictions': predictions,
                'comparison': comparison_result,
                'long_term_database_size': len(long_term_database),
                'short_term_database_size': len(short_term_database),
                'latest_period': latest_period
            })
            
            # 每100期显示一次进度（测试新功能）
            if (i + 1) % 100 == 0:
                ui.show_analysis_progress(
                    i + 1, len(analysis_periods),
                    len(long_term_database), len(short_term_database), latest_period
                )
        
        # 显示最终统计结果（测试新功能）
        ui.show_final_analysis_results(results)
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_ui_methods():
    """测试用户界面新方法"""
    print("\n=== 测试用户界面新方法 ===")
    
    try:
        ui = UserInterface()
        
        # 测试显示分析开始信息
        print("\n1. 测试显示分析开始信息:")
        ui.show_analysis_start_info(150)
        
        # 测试显示分析进度
        print("\n2. 测试显示分析进度:")
        latest_period = {
            'period': '25070',
            'red_balls': [1, 5, 10, 15, 20, 25],
            'blue_ball': 8
        }
        ui.show_analysis_progress(100, 150, 500, 50, latest_period)
        
        # 测试显示最终分析结果
        print("\n3. 测试显示最终分析结果:")
        # 模拟结果数据
        mock_results = []
        for i in range(5):
            mock_results.append({
                'comparison': {
                    1: {'max_hit_count': 4 if i < 2 else 3},
                    2: {'max_hit_count': 3 if i < 3 else 2}
                }
            })
        
        ui.show_final_analysis_results(mock_results)
        
        return True
        
    except Exception as e:
        print(f"UI方法测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("双色球彩票预测与分析系统 - 分析比对进度显示功能测试")
    print("=" * 70)
    
    # 检查数据文件
    if not os.path.exists("lottery_data_all.xlsx"):
        print("错误: 数据文件 'lottery_data_all.xlsx' 不存在！")
        return
    
    # 测试各个功能
    tests = [
        ("用户界面新方法", test_ui_methods),
        ("分析比对进度显示", test_analysis_progress_display),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name}测试出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 70)
    print("测试结果汇总:")
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"{test_name}: {status}")
    
    # 总体结果
    all_passed = all(result for _, result in results)
    print(f"\n总体测试结果: {'全部通过' if all_passed else '存在失败'}")
    
    if all_passed:
        print("\n分析比对进度显示功能测试通过！")


if __name__ == "__main__":
    main()
