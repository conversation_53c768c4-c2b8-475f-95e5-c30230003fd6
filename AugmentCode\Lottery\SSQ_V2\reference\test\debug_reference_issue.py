# -*- coding: utf-8 -*-
"""
调试引用问题
检查是否存在数据引用导致的显示错误
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ssq_lottery_system import SSQLotterySystem
import copy


def debug_reference_issue():
    """调试引用问题"""
    print("=== 调试数据引用问题 ===")
    
    try:
        # 创建系统实例
        system = SSQLotterySystem("lottery_data_all.xlsx")
        
        # 加载数据
        all_data = system.data_loader.get_current_database()
        
        # 找到期号25067的数据
        target_period = "25067"
        target_index = None
        
        for i, row in all_data.iterrows():
            if str(row['NO']) == target_period:
                target_index = i
                break
        
        if target_index is None:
            print(f"❌ 没有找到期号 {target_period}")
            return False
        
        print(f"✅ 找到期号 {target_period}，索引: {target_index}")
        
        # 获取数据库范围（前99期）
        database_range = 99
        start_index = max(0, target_index - database_range + 1)
        end_index = target_index + 1
        current_database = all_data.iloc[start_index:end_index].copy()
        
        # 获取答案数据（后6期）
        answer_start_index = target_index + 1
        answer_end_index = min(len(all_data), target_index + 7)
        answer_data_df = all_data.iloc[answer_start_index:answer_end_index].copy()
        
        # 转换答案数据格式
        answer_data = []
        for _, row in answer_data_df.iterrows():
            red_balls = [int(row[f'r{i}']) for i in range(1, 7)]
            blue_ball = int(row['b'])
            answer_data.append({
                'period': str(row['NO']),
                'red_balls': red_balls,
                'blue_ball': blue_ball
            })
        
        # 获取最新一期数据
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 运行分析器
        print(f"\n=== 运行分析器 ===")
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        print(f"\n=== 生成预测 ===")
        predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
        
        # 保存第9组预测的原始数据
        original_pred_9 = copy.deepcopy(predictions[9])
        print(f"原始第9组预测:")
        print(f"  红球: {original_pred_9['red_balls']}")
        print(f"  蓝球: {original_pred_9['blue_ball']}")
        
        # 进行比对分析
        print(f"\n=== 比对分析 ===")
        comparison_result = system.comparison_engine.compare_predictions(predictions, answer_data)
        
        # 检查比对后的预测数据是否发生变化
        print(f"\n=== 检查数据变化 ===")
        current_pred_9 = predictions[9]
        comparison_pred_9 = comparison_result[9]['prediction']
        
        print(f"当前第9组预测:")
        print(f"  红球: {current_pred_9['red_balls']}")
        print(f"  蓝球: {current_pred_9['blue_ball']}")
        
        print(f"比对结果中的第9组预测:")
        print(f"  红球: {comparison_pred_9['red_balls']}")
        print(f"  蓝球: {comparison_pred_9['blue_ball']}")
        
        # 检查是否是同一个对象
        print(f"\n=== 对象引用检查 ===")
        print(f"predictions[9] is comparison_result[9]['prediction']: {predictions[9] is comparison_result[9]['prediction']}")
        print(f"original_pred_9 == current_pred_9: {original_pred_9 == current_pred_9}")
        
        # 检查内存地址
        print(f"predictions[9] id: {id(predictions[9])}")
        print(f"comparison_result[9]['prediction'] id: {id(comparison_result[9]['prediction'])}")
        print(f"original_pred_9 id: {id(original_pred_9)}")
        
        # 模拟修改预测数据，看是否影响比对结果
        print(f"\n=== 模拟数据修改 ===")
        print(f"修改前 predictions[9]['red_balls'][0]: {predictions[9]['red_balls'][0]}")
        print(f"修改前 comparison_result[9]['prediction']['red_balls'][0]: {comparison_result[9]['prediction']['red_balls'][0]}")
        
        # 修改预测数据
        old_value = predictions[9]['red_balls'][0]
        predictions[9]['red_balls'][0] = 99  # 修改为一个明显不同的值
        
        print(f"修改后 predictions[9]['red_balls'][0]: {predictions[9]['red_balls'][0]}")
        print(f"修改后 comparison_result[9]['prediction']['red_balls'][0]: {comparison_result[9]['prediction']['red_balls'][0]}")
        
        if predictions[9]['red_balls'][0] == comparison_result[9]['prediction']['red_balls'][0]:
            print("❌ 发现引用问题！修改预测数据影响了比对结果中的数据")
        else:
            print("✅ 没有引用问题")
        
        # 恢复原值
        predictions[9]['red_balls'][0] = old_value
        
        # 检查打印功能是否受影响
        print(f"\n=== 检查打印功能 ===")
        
        # 模拟 _print_high_hit_details 的逻辑
        max_hit = comparison_result[9]['max_hit']
        prediction = comparison_result[9]['prediction']
        
        if max_hit['total_hits'] >= 6:
            red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
            print(f"打印功能会显示:")
            print(f"  预测红球: {red_balls_str}")
            print(f"  预测蓝球: {prediction['blue_ball']:2d}")
            print(f"  最大命中数: {max_hit['total_hits']}球")
            print(f"  红球命中数: {max_hit['red_hits']}个")
            print(f"  蓝球命中数: {max_hit['blue_hits']}个")
            
            # 检查蓝球命中状态的计算
            pred_blue = int(prediction['blue_ball'])
            answer_blue = int(max_hit['answer_blue_ball'])
            manual_blue_hits = 1 if pred_blue == answer_blue else 0
            
            print(f"\n蓝球命中验证:")
            print(f"  预测蓝球: {pred_blue}")
            print(f"  答案蓝球: {answer_blue}")
            print(f"  手动计算蓝球命中: {manual_blue_hits}")
            print(f"  比对引擎蓝球命中: {max_hit['blue_hits']}")
            print(f"  蓝球命中状态: {max_hit['blue_hit_status']}")
            
            if manual_blue_hits != max_hit['blue_hits']:
                print("❌ 蓝球命中计算错误！")
            else:
                print("✅ 蓝球命中计算正确")
        
        return True
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    debug_reference_issue()
