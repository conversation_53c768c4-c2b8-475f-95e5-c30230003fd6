# -*- coding: utf-8 -*-
"""
调试用户截图中的问题
根据截图信息：
- 分析期号: 25067
- 预测组号: 第9组
- 预测方法: 马尔科夫链+红球重号筛选
- 预测红球: 2 7 10 22 27 33
- 预测蓝球: 14
- 最大命中数: 6球
- 最大命中期号: 25073
- 命中期号红球: 2 7 10 27 30 33
- 命中期号蓝球: 11
- 红球命中数: 5个
- 蓝球命中数: 1个
- 蓝球命中状态: 是
- 命中红球详情: 2 7 10 27 33

问题：红球命中5个，蓝球命中1个，总共应该是6个，但蓝球实际没有命中（预测14，实际11）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ssq_lottery_system import SSQLotterySystem


def debug_screenshot_issue():
    """调试截图中的问题"""
    print("=== 调试用户截图中的问题 ===")
    
    try:
        # 创建系统实例
        system = SSQLotterySystem("lottery_data_all.xlsx")
        
        # 加载数据
        all_data = system.data_loader.get_current_database()
        print(f"总数据量: {len(all_data)}")

        # 找到期号25067的数据
        target_period = "25067"
        target_index = None

        for i, row in all_data.iterrows():
            if str(row['NO']) == target_period:
                target_index = i
                break
        
        if target_index is None:
            print(f"❌ 没有找到期号 {target_period}")
            return False
        
        print(f"✅ 找到期号 {target_period}，索引: {target_index}")
        
        # 获取数据库范围（前99期）
        database_range = 99
        start_index = max(0, target_index - database_range + 1)
        end_index = target_index + 1
        current_database = all_data.iloc[start_index:end_index].copy()
        
        print(f"数据库范围: 索引 {start_index} 到 {end_index-1}")
        print(f"数据库期号范围: {current_database.iloc[0]['NO']} 到 {current_database.iloc[-1]['NO']}")

        # 获取答案数据（后6期）
        answer_start_index = target_index + 1
        answer_end_index = min(len(all_data), target_index + 7)
        answer_data_df = all_data.iloc[answer_start_index:answer_end_index].copy()

        print(f"答案数据范围: 索引 {answer_start_index} 到 {answer_end_index-1}")
        print(f"答案数据期号: {list(answer_data_df['NO'])}")

        # 转换答案数据格式
        answer_data = []
        for _, row in answer_data_df.iterrows():
            red_balls = [int(row[f'r{i}']) for i in range(1, 7)]
            blue_ball = int(row['b'])
            answer_data.append({
                'period': str(row['NO']),
                'red_balls': red_balls,
                'blue_ball': blue_ball
            })
        
        print(f"\n答案数据详情:")
        for answer in answer_data:
            red_str = ' '.join(f"{ball:2d}" for ball in answer['red_balls'])
            print(f"  期号{answer['period']}: {red_str} + {answer['blue_ball']:2d}")
        
        # 获取最新一期数据
        latest_period = system.data_loader.get_latest_period(current_database)
        print(f"最新一期: {latest_period}")

        # 运行分析器（必须在生成预测之前）
        print(f"\n=== 运行分析器 ===")
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)

        # 生成预测
        print(f"\n=== 生成预测 ===")
        predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
        
        # 检查第9组预测
        if 9 not in predictions:
            print(f"❌ 没有找到第9组预测")
            return False
        
        prediction_9 = predictions[9]
        print(f"✅ 第9组预测:")
        print(f"  方法: {prediction_9['method']}")
        print(f"  红球: {prediction_9['red_balls']}")
        print(f"  蓝球: {prediction_9['blue_ball']}")
        
        # 检查是否与截图一致
        expected_red = [2, 7, 10, 22, 27, 33]
        expected_blue = 14
        
        actual_red = sorted(prediction_9['red_balls'])
        actual_blue = prediction_9['blue_ball']
        
        print(f"\n=== 与截图对比 ===")
        print(f"期望红球: {expected_red}")
        print(f"实际红球: {actual_red}")
        print(f"红球匹配: {'✅' if actual_red == expected_red else '❌'}")
        
        print(f"期望蓝球: {expected_blue}")
        print(f"实际蓝球: {actual_blue}")
        print(f"蓝球匹配: {'✅' if actual_blue == expected_blue else '❌'}")
        
        # 进行比对分析
        print(f"\n=== 比对分析 ===")
        comparison_result = system.comparison_engine.compare_predictions(predictions, answer_data)
        
        if 9 not in comparison_result:
            print(f"❌ 没有找到第9组比对结果")
            return False
        
        group_9_result = comparison_result[9]
        max_hit = group_9_result['max_hit']
        
        print(f"第9组比对结果:")
        print(f"  最大命中期号: {max_hit['period']}")
        print(f"  红球命中数: {max_hit['red_hits']}")
        print(f"  蓝球命中数: {max_hit['blue_hits']}")
        print(f"  总命中数: {max_hit['total_hits']}")
        print(f"  蓝球命中状态: {max_hit['blue_hit_status']}")
        
        # 手动验证最大命中期号的比对
        print(f"\n=== 手动验证期号 {max_hit['period']} ===")
        
        # 找到对应的答案数据
        target_answer = None
        for answer in answer_data:
            if answer['period'] == max_hit['period']:
                target_answer = answer
                break
        
        if target_answer is None:
            print(f"❌ 没有找到期号 {max_hit['period']} 的答案数据")
            return False
        
        print(f"预测红球: {sorted(prediction_9['red_balls'])}")
        print(f"答案红球: {sorted(target_answer['red_balls'])}")
        print(f"预测蓝球: {prediction_9['blue_ball']}")
        print(f"答案蓝球: {target_answer['blue_ball']}")
        
        # 手动计算命中
        pred_red_set = set(prediction_9['red_balls'])
        answer_red_set = set(target_answer['red_balls'])
        hit_red_balls = pred_red_set & answer_red_set
        manual_red_hits = len(hit_red_balls)
        
        manual_blue_hits = 1 if prediction_9['blue_ball'] == target_answer['blue_ball'] else 0
        manual_total_hits = manual_red_hits + manual_blue_hits
        
        print(f"\n手动计算结果:")
        print(f"  命中红球: {sorted(list(hit_red_balls))}")
        print(f"  红球命中数: {manual_red_hits}")
        print(f"  蓝球命中数: {manual_blue_hits}")
        print(f"  总命中数: {manual_total_hits}")
        
        print(f"\n比对引擎结果:")
        print(f"  红球命中数: {max_hit['red_hits']}")
        print(f"  蓝球命中数: {max_hit['blue_hits']}")
        print(f"  总命中数: {max_hit['total_hits']}")
        
        # 检查是否一致
        if (manual_red_hits == max_hit['red_hits'] and 
            manual_blue_hits == max_hit['blue_hits'] and 
            manual_total_hits == max_hit['total_hits']):
            print(f"\n✅ 比对引擎计算正确")
        else:
            print(f"\n❌ 比对引擎计算错误！")
            print(f"  手动: 红{manual_red_hits}+蓝{manual_blue_hits}={manual_total_hits}")
            print(f"  引擎: 红{max_hit['red_hits']}+蓝{max_hit['blue_hits']}={max_hit['total_hits']}")
        
        # 检查所有期号的比对结果
        print(f"\n=== 所有期号比对结果 ===")
        for result in group_9_result['results']:
            print(f"期号{result['period']}: 红{result['red_hits']}+蓝{result['blue_hits']}={result['total_hits']}")
        
        # 模拟打印功能
        print(f"\n=== 模拟打印功能 ===")
        if max_hit['total_hits'] >= 6:
            red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction_9['red_balls'])
            answer_red_str = ' '.join(f"{ball:2d}" for ball in max_hit['answer_red_balls'])
            
            print(f"🔥 【{max_hit['total_hits']}球命中】")
            print(f"   分析期号: {target_period}")
            print(f"   预测组号: 第9组")
            print(f"   预测方法: {prediction_9['method']}")
            print(f"   预测红球: {red_balls_str}")
            print(f"   预测蓝球: {prediction_9['blue_ball']:2d}")
            print(f"   最大命中数: {max_hit['total_hits']}球")
            print(f"   最大命中期号: {max_hit['period']}")
            print(f"   命中期号红球: {answer_red_str}")
            print(f"   命中期号蓝球: {max_hit['answer_blue_ball']:2d}")
            print(f"   红球命中数: {max_hit['red_hits']}个")
            print(f"   蓝球命中数: {max_hit['blue_hits']}个")
            print(f"   蓝球命中状态: {'是' if max_hit['blue_hits'] == 1 else '否'}")
            
            # 显示具体命中的红球
            hit_red_str = ' '.join(f"{ball:2d}" for ball in sorted(list(hit_red_balls)))
            print(f"   命中红球详情: {hit_red_str}")
        
        return True
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    debug_screenshot_issue()
