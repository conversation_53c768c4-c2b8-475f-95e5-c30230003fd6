# 双色球彩票预测与分析系统 V1.0

一个基于Python开发的双色球彩票预测与分析系统，实现了24种预测算法，包括历史概率分析、马尔科夫链算法、贝叶斯概率算法以及多种筛选策略组合。

## 🎯 系统特性

### 核心算法
- **历史出现概率分析**: 基于历史数据统计各号码出现频率
- **马尔科夫链算法**: 分析号码间的转移概率关系
- **贝叶斯概率算法**: 结合先验概率和条件概率进行预测
- **多维度筛选**: 大球数、冷球数、重号数等多种筛选策略

### 预测方法
系统提供24种预测方法，涵盖：
1. 基础算法（3种）：历史概率、马尔科夫链、贝叶斯概率
2. 单一筛选（9种）：基础算法 + 大球/冷球/重号筛选
3. 组合筛选（12种）：多种筛选条件的组合应用

### 分析功能
- **历史数据分析**: 支持自定义数据范围分析
- **预测结果比对**: 将预测结果与实际开奖结果进行比对
- **统计分析**: 命中率统计、分布分析等
- **Excel导出**: 完整的预测结果和概率表格导出

## 📁 项目结构

```
SSQ_V1/
├── ssq_lottery_system.py      # 主程序入口
├── demo.py                    # 功能演示脚本
├── test_system.py             # 系统测试脚本
├── lottery_data_all.xlsx      # 历史数据文件
├── README.md                  # 项目说明文档
├── modules/                   # 核心模块目录
│   ├── __init__.py
│   ├── data_loader.py         # 数据加载模块
│   ├── statistical_analyzer.py # 统计分析模块
│   ├── markov_chain.py        # 马尔科夫链分析模块
│   ├── bayesian_analyzer.py   # 贝叶斯分析模块
│   ├── prediction_engine.py   # 预测引擎模块
│   ├── prediction_helpers.py  # 预测辅助函数
│   ├── constraint_helpers.py  # 约束辅助函数
│   ├── comparison_engine.py   # 比对引擎模块
│   ├── user_interface.py      # 用户界面模块
│   └── export_manager.py      # 导出管理模块
└── output/                    # 输出文件目录
    └── *.xlsx                 # 导出的Excel文件
```

## 🚀 快速开始

### 环境要求
- Python 3.7+
- pandas
- numpy
- openpyxl

### 安装依赖
```bash
pip install pandas numpy openpyxl
```

### 运行方式

#### 1. 交互式运行
```bash
python ssq_lottery_system.py
```
启动完整的交互式系统，支持预测选号和分析比对两种模式。

#### 2. 功能演示
```bash
python demo.py
```
运行功能演示脚本，展示系统的主要功能。

#### 3. 系统测试
```bash
python test_system.py
```
运行系统功能测试，验证各模块是否正常工作。

## 📊 使用说明

### 预测选号模式
1. 选择数据库范围（输入0使用全部数据，或输入正整数使用最近N期数据）
2. 系统自动分析历史数据，计算各种概率
3. 生成24组预测号码
4. 可选择导出结果到Excel文件

### 分析比对模式
1. 输入要分析的起始期号
2. 选择数据库范围
3. 系统自动进行批量分析比对
4. 显示命中统计和分布分析
5. 自动保存分析结果

## 🔧 核心算法说明

### 1. 历史出现概率
基于历史数据统计每个号码的出现频率，计算概率分布。

### 2. 马尔科夫链算法
分析号码间的转移关系，基于最新一期号码计算下期各号码的出现概率。

### 3. 贝叶斯概率算法
结合历史概率和转移概率，使用贝叶斯公式计算综合概率。

### 4. 筛选策略
- **大球筛选**: 根据大球数历史分布进行筛选
- **冷球筛选**: 基于冷球（近期未出现号码）进行筛选
- **重号筛选**: 根据重号（与上期相同号码）进行筛选

## 📈 输出结果

### 预测结果
每组预测包含：
- 预测方法名称
- 6个红球号码（1-33）
- 1个蓝球号码（1-16）

### 分析结果
- 各组预测的最大命中情况
- 命中分布统计
- 蓝球命中率分析
- 方法效果对比

### Excel导出
- 预测结果表
- 各种概率表格
- 比对分析结果
- 统计汇总信息

## 🎲 术语定义

- **期号**: 每期的编号，5位数字格式（如25081表示2025年第81期）
- **红球**: 双色球的前6个号码，范围1-33
- **蓝球**: 双色球的第7个号码，范围1-16
- **大球**: 红球>16或蓝球>8的号码
- **冷球**: 在最近5期中未出现的号码
- **重号**: 与上一期相同的号码

## ⚠️ 注意事项

1. **数据文件**: 确保`lottery_data_all.xlsx`文件存在且格式正确
2. **数据范围**: 分析时需要足够的历史数据支持
3. **预测性质**: 本系统仅供学习研究，彩票具有随机性，预测结果不保证准确性
4. **文件权限**: 确保有写入权限以保存导出文件

## 🔍 测试验证

系统通过了全面的功能测试：
- ✅ 数据加载功能测试
- ✅ 统计分析功能测试  
- ✅ 预测算法功能测试
- ✅ 比对引擎功能测试
- ✅ 系统集成功能测试

运行`python test_system.py`查看详细测试结果。

## 📝 开发信息

- **版本**: V1.0
- **开发语言**: Python 3
- **开发时间**: 2025年7月
- **代码行数**: 约3000行
- **模块数量**: 10个核心模块

## 🤝 贡献

欢迎提出改进建议和bug报告。本项目仅供学习交流使用。

---

**免责声明**: 本系统仅供学习研究使用，不构成任何投注建议。彩票具有随机性，请理性对待。
