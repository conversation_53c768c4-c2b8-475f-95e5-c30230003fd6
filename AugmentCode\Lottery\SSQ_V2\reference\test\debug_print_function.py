#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试高命中数打印功能
检查实际运行时的数据是否正确
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def debug_print_function():
    """调试高命中数打印功能"""
    print("=== 调试高命中数打印功能 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    try:
        # 分析25067期
        period = '25067'
        database_range = 99
        
        print(f"分析期号: {period}")
        print(f"数据库范围: {database_range}期")
        
        # 获取数据库和答案
        current_database = system.data_loader.get_database_for_period(period, database_range)
        answer_data = system.data_loader.get_answer_data(period)
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 分析
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
        
        # 比对结果
        comparison_result = system.comparison_engine.compare_predictions(predictions, answer_data)
        
        # 查找第9组（用户截图中的组）
        if 9 in predictions and 9 in comparison_result:
            prediction = predictions[9]
            group_result = comparison_result[9]
            max_hit = group_result['max_hit']
            
            print(f"\n第9组详细信息:")
            print(f"  预测红球: {prediction['red_balls']}")
            print(f"  预测蓝球: {prediction['blue_ball']}")
            print(f"  预测方法: {prediction['method']}")
            
            print(f"\n比对引擎返回的max_hit:")
            print(f"  period: {max_hit['period']}")
            print(f"  total_hits: {max_hit['total_hits']}")
            print(f"  red_hits: {max_hit['red_hits']}")
            print(f"  blue_hits: {max_hit['blue_hits']}")
            print(f"  blue_hit_status: {max_hit['blue_hit_status']}")
            print(f"  answer_red_balls: {max_hit['answer_red_balls']}")
            print(f"  answer_blue_ball: {max_hit['answer_blue_ball']}")
            
            # 手动验证计算
            print(f"\n手动验证计算:")
            pred_red_set = set([int(x) for x in prediction['red_balls']])
            pred_blue = int(prediction['blue_ball'])
            answer_red_set = set([int(x) for x in max_hit['answer_red_balls']])
            answer_blue = int(max_hit['answer_blue_ball'])
            
            manual_red_hits = len(pred_red_set & answer_red_set)
            manual_blue_hits = 1 if pred_blue == answer_blue else 0
            manual_total_hits = manual_red_hits + manual_blue_hits
            
            print(f"  预测红球集合: {pred_red_set}")
            print(f"  答案红球集合: {answer_red_set}")
            print(f"  红球交集: {pred_red_set & answer_red_set}")
            print(f"  手动红球命中: {manual_red_hits}")
            print(f"  预测蓝球: {pred_blue}, 答案蓝球: {answer_blue}")
            print(f"  手动蓝球命中: {manual_blue_hits}")
            print(f"  手动总命中: {manual_total_hits}")
            
            # 检查一致性
            if (max_hit['red_hits'] == manual_red_hits and
                max_hit['blue_hits'] == manual_blue_hits and
                max_hit['total_hits'] == manual_total_hits):
                print(f"  ✅ 比对引擎数据一致")
            else:
                print(f"  ❌ 比对引擎数据不一致")
                print(f"    比对引擎: 红{max_hit['red_hits']}+蓝{max_hit['blue_hits']}={max_hit['total_hits']}")
                print(f"    手动计算: 红{manual_red_hits}+蓝{manual_blue_hits}={manual_total_hits}")
            
            # 检查所有答案数据
            print(f"\n检查所有答案数据的命中情况:")
            for answer in answer_data:
                answer_red_set = set([int(x) for x in answer['red_balls']])
                answer_blue = int(answer['blue_ball'])
                
                red_hits = len(pred_red_set & answer_red_set)
                blue_hits = 1 if pred_blue == answer_blue else 0
                total_hits = red_hits + blue_hits
                
                red_str = ' '.join(f"{ball:2d}" for ball in answer['red_balls'])
                print(f"    期号{answer['period']}: {red_str} + {answer_blue:2d} -> 红{red_hits}+蓝{blue_hits}={total_hits}球")
            
            # 模拟打印功能的逻辑
            print(f"\n模拟打印功能:")
            if max_hit['total_hits'] >= 6:
                print(f"  会显示为6球命中（错误）")
                print(f"  实际应该显示为{manual_total_hits}球命中")
            else:
                print(f"  不会显示（正确，因为只有{manual_total_hits}球命中）")
        
        else:
            print(f"❌ 没有找到第9组的预测或比对结果")
            
            # 显示所有组的情况
            print(f"\n所有组的命中情况:")
            for group_id in range(1, 25):
                if group_id in predictions and group_id in comparison_result:
                    prediction = predictions[group_id]
                    group_result = comparison_result[group_id]
                    max_hit = group_result['max_hit']
                    
                    red_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
                    print(f"  第{group_id:2d}组: {red_str} + {prediction['blue_ball']:2d} -> "
                          f"红{max_hit['red_hits']}+蓝{max_hit['blue_hits']}={max_hit['total_hits']}球")
                    
                    if max_hit['total_hits'] >= 6:
                        print(f"    ⚠️ 这组会被显示为高命中")
        
        return True
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("调试高命中数打印功能")
    print("=" * 60)
    
    success = debug_print_function()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 调试完成")
    else:
        print("❌ 调试失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
