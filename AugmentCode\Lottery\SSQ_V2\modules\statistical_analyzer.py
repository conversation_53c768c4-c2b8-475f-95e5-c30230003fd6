# -*- coding: utf-8 -*-
"""
统计分析模块 (Statistical Analyzer Module)

负责计算历史出现概率、跟随性概率等核心统计功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from collections import Counter


class StatisticalAnalyzer:
    """
    统计分析器类
    
    负责各种统计分析计算
    """
    
    def __init__(self, config: Dict):
        """
        初始化统计分析器
        
        Args:
            config: 系统配置字典
        """
        self.config = config
        self.database = None
        
        # 概率表格存储
        self.red_ball_probabilities = None
        self.blue_ball_probabilities = None
        self.red_follow_matrix = None
        self.blue_follow_matrix = None
    
    def analyze(self, database: pd.DataFrame):
        """
        执行完整的统计分析
        
        Args:
            database: 数据库DataFrame
        """
        self.database = database
        
        # 计算历史出现概率
        self._calculate_historical_probabilities()
        
        # 计算跟随性概率矩阵
        self._calculate_follow_probabilities()
    
    def _calculate_historical_probabilities(self):
        """计算历史出现概率"""
        # 红球历史出现概率
        red_counts = Counter()
        for i in range(1, 7):
            red_counts.update(self.database[f'r{i}'].tolist())
        
        total_red = sum(red_counts.values())
        self.red_ball_probabilities = {}
        for ball in range(1, 34):
            count = red_counts.get(ball, 0)
            self.red_ball_probabilities[ball] = count / total_red if total_red > 0 else 0
        
        # 蓝球历史出现概率
        blue_counts = Counter(self.database['b'].tolist())
        total_blue = sum(blue_counts.values())
        self.blue_ball_probabilities = {}
        for ball in range(1, 17):
            count = blue_counts.get(ball, 0)
            self.blue_ball_probabilities[ball] = count / total_blue if total_blue > 0 else 0
    
    def _calculate_follow_probabilities(self):
        """计算跟随性概率矩阵"""
        # 红球跟随性概率矩阵（33x33）
        self.red_follow_matrix = np.zeros((33, 33))
        
        # 蓝球跟随性概率矩阵（16x16）
        self.blue_follow_matrix = np.zeros((16, 16))
        
        # 计算相邻两期的跟随关系
        for i in range(len(self.database) - 1):
            current_row = self.database.iloc[i]
            next_row = self.database.iloc[i + 1]
            
            # 当前期红球
            current_red = [int(current_row[f'r{j}']) for j in range(1, 7)]
            # 下一期红球
            next_red = [int(next_row[f'r{j}']) for j in range(1, 7)]
            
            # 统计红球跟随关系
            for curr_ball in current_red:
                for next_ball in next_red:
                    self.red_follow_matrix[curr_ball - 1][next_ball - 1] += 1
            
            # 蓝球跟随关系
            current_blue = int(current_row['b'])
            next_blue = int(next_row['b'])
            self.blue_follow_matrix[current_blue - 1][next_blue - 1] += 1
        
        # 转换为概率
        # 红球跟随概率矩阵：每列的概率之和为1
        for col in range(33):
            col_sum = np.sum(self.red_follow_matrix[:, col])
            if col_sum > 0:
                self.red_follow_matrix[:, col] /= col_sum
        
        # 蓝球跟随概率矩阵：每列的概率之和为1
        for col in range(16):
            col_sum = np.sum(self.blue_follow_matrix[:, col])
            if col_sum > 0:
                self.blue_follow_matrix[:, col] /= col_sum
    
    def get_probability_tables(self) -> Dict:
        """
        获取所有概率表格
        
        Returns:
            概率表格字典
        """
        return {
            'red_ball_probabilities': self.red_ball_probabilities,
            'blue_ball_probabilities': self.blue_ball_probabilities,
            'red_follow_matrix': self.red_follow_matrix,
            'blue_follow_matrix': self.blue_follow_matrix
        }
    
    def get_top_red_balls_by_probability(self, count: int) -> List[int]:
        """
        根据历史出现概率获取前N个红球号码
        
        Args:
            count: 需要获取的红球数量
            
        Returns:
            红球号码列表
        """
        if self.red_ball_probabilities is None:
            return []
        
        # 按概率排序
        sorted_balls = sorted(
            self.red_ball_probabilities.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        return [ball for ball, prob in sorted_balls[:count]]
    
    def get_top_blue_balls_by_probability(self, count: int) -> List[int]:
        """
        根据历史出现概率获取前N个蓝球号码
        
        Args:
            count: 需要获取的蓝球数量
            
        Returns:
            蓝球号码列表
        """
        if self.blue_ball_probabilities is None:
            return []
        
        # 按概率排序
        sorted_balls = sorted(
            self.blue_ball_probabilities.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        return [ball for ball, prob in sorted_balls[:count]]
    
    def get_red_ball_probability(self, ball: int) -> float:
        """
        获取指定红球的历史出现概率
        
        Args:
            ball: 红球号码
            
        Returns:
            概率值
        """
        if self.red_ball_probabilities is None:
            return 0.0
        
        return self.red_ball_probabilities.get(ball, 0.0)
    
    def get_blue_ball_probability(self, ball: int) -> float:
        """
        获取指定蓝球的历史出现概率
        
        Args:
            ball: 蓝球号码
            
        Returns:
            概率值
        """
        if self.blue_ball_probabilities is None:
            return 0.0
        
        return self.blue_ball_probabilities.get(ball, 0.0)
    
    def get_red_follow_probability(self, from_ball: int, to_ball: int) -> float:
        """
        获取红球跟随概率
        
        Args:
            from_ball: 前一期红球号码
            to_ball: 后一期红球号码
            
        Returns:
            跟随概率
        """
        if self.red_follow_matrix is None:
            return 0.0
        
        if 1 <= from_ball <= 33 and 1 <= to_ball <= 33:
            return self.red_follow_matrix[to_ball - 1][from_ball - 1]
        
        return 0.0
    
    def get_blue_follow_probability(self, from_ball: int, to_ball: int) -> float:
        """
        获取蓝球跟随概率
        
        Args:
            from_ball: 前一期蓝球号码
            to_ball: 后一期蓝球号码
            
        Returns:
            跟随概率
        """
        if self.blue_follow_matrix is None:
            return 0.0
        
        if 1 <= from_ball <= 16 and 1 <= to_ball <= 16:
            return self.blue_follow_matrix[to_ball - 1][from_ball - 1]
        
        return 0.0
