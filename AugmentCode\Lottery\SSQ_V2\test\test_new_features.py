#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新功能的脚本

测试目标期号输入0表示最新期的功能
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.data_loader import DataLoader
from modules.user_interface import UserInterface


def test_target_period_input():
    """测试目标期号输入功能"""
    print("=== 测试目标期号输入功能 ===")
    
    try:
        # 初始化数据加载器
        data_loader = DataLoader("lottery_data_all.xlsx")
        ui = UserInterface()
        
        print(f"原始数据库最新期号: {max(data_loader.original_database['NO'])}")
        print(f"原始数据库最早期号: {min(data_loader.original_database['NO'])}")
        print(f"原始数据库总期数: {len(data_loader.original_database)}")
        
        # 模拟用户输入0
        print("\n模拟用户输入 '0':")
        
        # 手动测试输入0的逻辑
        period_input = "0"
        if period_input == "0":
            latest_period = str(max(data_loader.original_database['NO']))
            print(f"使用原始数据库最新期号: {latest_period}")
        
        # 测试有效期号
        print("\n测试有效期号:")
        test_periods = ["25001", "25050", "25081"]
        for period in test_periods:
            period_int = int(period)
            if period_int in data_loader.original_database['NO'].values:
                print(f"期号 {period}: 存在于数据库中")
            else:
                print(f"期号 {period}: 不存在于数据库中")
        
        # 测试无效期号
        print("\n测试无效期号:")
        invalid_periods = ["99999", "12345"]
        for period in invalid_periods:
            period_int = int(period)
            if period_int in data_loader.original_database['NO'].values:
                print(f"期号 {period}: 存在于数据库中")
            else:
                print(f"期号 {period}: 不存在于数据库中")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False


def test_prediction_with_latest_period():
    """测试使用最新期号进行预测"""
    print("\n=== 测试使用最新期号进行预测 ===")
    
    try:
        from modules.statistical_analyzer import StatisticalAnalyzer
        from modules.markov_chain import MarkovChainAnalyzer
        from modules.prediction_engine import PredictionEngine
        
        # 系统配置
        config = {
            'red_ball_range': (1, 33),
            'blue_ball_range': (1, 16),
            'red_ball_count': 6,
            'blue_ball_count': 1
        }
        
        # 初始化组件
        data_loader = DataLoader("lottery_data_all.xlsx")
        statistical_analyzer = StatisticalAnalyzer(config)
        markov_analyzer = MarkovChainAnalyzer(config)
        prediction_engine = PredictionEngine(config, statistical_analyzer, markov_analyzer)
        
        # 获取最新期号
        latest_period_str = str(max(data_loader.original_database['NO']))
        print(f"使用最新期号: {latest_period_str}")
        
        # 设置参数
        long_term_range = 500
        short_term_range = 50
        
        # 获取数据
        long_term_database = data_loader.get_database_for_period(latest_period_str, long_term_range)
        short_term_database = data_loader.get_database_for_period(latest_period_str, short_term_range)
        
        if long_term_database is None or short_term_database is None:
            print("数据获取失败！")
            return False
        
        print(f"远期数据库: {len(long_term_database)}期")
        print(f"近期数据库: {len(short_term_database)}期")
        
        # 获取最新一期信息
        latest_period = data_loader.get_latest_period(long_term_database)
        if latest_period:
            red_balls_str = ' '.join(f"{ball:2d}" for ball in latest_period['red_balls'])
            print(f"最新一期: {latest_period['period']} {red_balls_str} + {latest_period['blue_ball']:2d}")
        
        # 运行分析
        print("\n正在进行统计分析...")
        statistical_analyzer.analyze(long_term_database)
        long_term_stats = statistical_analyzer.get_probability_tables()
        
        statistical_analyzer.analyze(short_term_database)
        short_term_stats = statistical_analyzer.get_probability_tables()
        
        print("正在进行马尔科夫链分析...")
        markov_analyzer.analyze(long_term_database, latest_period)
        long_term_markov = markov_analyzer.get_probability_tables()
        
        markov_analyzer.analyze(short_term_database, latest_period)
        short_term_markov = markov_analyzer.get_probability_tables()
        
        # 生成预测
        print("正在生成预测...")
        predictions = prediction_engine.generate_predictions(
            long_term_stats, short_term_stats,
            long_term_markov, short_term_markov,
            latest_period
        )
        
        # 显示预测结果
        print("\n=== 预测结果 ===")
        for group_id, prediction in predictions.items():
            red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
            blue_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['blue_balls'])
            print(f"第{group_id}组预测的号码为：{red_balls_str} + {blue_balls_str} {prediction['method']}")
        
        return True
        
    except Exception as e:
        print(f"预测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("双色球彩票预测与分析系统 - 新功能测试")
    print("=" * 60)
    
    # 检查数据文件
    if not os.path.exists("lottery_data_all.xlsx"):
        print("错误: 数据文件 'lottery_data_all.xlsx' 不存在！")
        return
    
    # 测试各个新功能
    tests = [
        ("目标期号输入功能", test_target_period_input),
        ("使用最新期号预测", test_prediction_with_latest_period),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name}测试出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"{test_name}: {status}")
    
    # 总体结果
    all_passed = all(result for _, result in results)
    print(f"\n总体测试结果: {'全部通过' if all_passed else '存在失败'}")
    
    if all_passed:
        print("\n新功能测试通过，可以使用主程序！")


if __name__ == "__main__":
    main()
