# -*- coding: utf-8 -*-
"""
导出管理模块 (Export Manager Module)

负责将预测结果、统计数据和概率表格导出到Excel文件。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional
from datetime import datetime
import os


class ExportManager:
    """
    导出管理器类
    
    负责数据导出功能
    """
    
    def __init__(self):
        """初始化导出管理器"""
        self.output_dir = "output"
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def export_prediction_results(self, predictions: Dict, probability_tables: Dict, database):
        """
        导出预测结果
        
        Args:
            predictions: 预测结果字典
            probability_tables: 概率表格字典
            database: 数据库DataFrame
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.output_dir}/prediction_results_{timestamp}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 导出预测结果
            self._export_predictions_sheet(predictions, writer)
            
            # 导出概率表格
            self._export_probability_tables(probability_tables, writer)
            
            # 导出数据库信息
            self._export_database_info(database, writer)
        
        print(f"预测结果已导出到: {filename}")
    
    def export_analysis_results(self, results: List[Dict]):
        """
        导出分析结果
        
        Args:
            results: 分析结果列表
        """
        if not results:
            print("没有分析结果可导出。")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.output_dir}/analysis_results_{timestamp}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 导出分析摘要
            self._export_analysis_summary(results, writer)
            
            # 导出详细比对结果
            self._export_detailed_comparison(results, writer)
            
            # 导出统计分析
            self._export_statistical_analysis(results, writer)
        
        print(f"分析结果已导出到: {filename}")
    
    def _export_predictions_sheet(self, predictions: Dict, writer):
        """导出预测结果工作表"""
        prediction_data = []
        
        for group_id in sorted(predictions.keys()):
            prediction = predictions[group_id]
            red_balls_str = ' '.join(map(str, prediction['red_balls']))
            
            prediction_data.append({
                '组号': group_id,
                '预测方法': prediction['method'],
                '红球号码': red_balls_str,
                '蓝球号码': prediction['blue_ball'],
                '完整号码': f"{red_balls_str} + {prediction['blue_ball']}"
            })
        
        df_predictions = pd.DataFrame(prediction_data)
        df_predictions.to_excel(writer, sheet_name='预测结果', index=False)
    
    def _export_probability_tables(self, probability_tables: Dict, writer):
        """导出概率表格"""
        # 红球历史出现概率
        if 'statistical' in probability_tables and 'red_ball_probabilities' in probability_tables['statistical']:
            red_prob_data = []
            red_probs = probability_tables['statistical']['red_ball_probabilities']
            for ball in range(1, 34):
                red_prob_data.append({
                    '红球号码': ball,
                    '历史出现概率': red_probs.get(ball, 0)
                })
            
            df_red_prob = pd.DataFrame(red_prob_data)
            df_red_prob.to_excel(writer, sheet_name='红球历史概率', index=False)
        
        # 蓝球历史出现概率
        if 'statistical' in probability_tables and 'blue_ball_probabilities' in probability_tables['statistical']:
            blue_prob_data = []
            blue_probs = probability_tables['statistical']['blue_ball_probabilities']
            for ball in range(1, 17):
                blue_prob_data.append({
                    '蓝球号码': ball,
                    '历史出现概率': blue_probs.get(ball, 0)
                })
            
            df_blue_prob = pd.DataFrame(blue_prob_data)
            df_blue_prob.to_excel(writer, sheet_name='蓝球历史概率', index=False)
        
        # 大球数概率
        if 'statistical' in probability_tables:
            big_ball_data = []
            
            red_big_probs = probability_tables['statistical'].get('red_big_ball_probabilities', {})
            for count in range(7):
                big_ball_data.append({
                    '红球大球数': count,
                    '历史出现概率': red_big_probs.get(count, 0)
                })
            
            df_big_ball = pd.DataFrame(big_ball_data)
            df_big_ball.to_excel(writer, sheet_name='红球大球数概率', index=False)
            
            # 蓝球大球数概率
            blue_big_data = []
            blue_big_probs = probability_tables['statistical'].get('blue_big_ball_probabilities', {})
            for count in range(2):
                blue_big_data.append({
                    '蓝球大球数': count,
                    '历史出现概率': blue_big_probs.get(count, 0)
                })
            
            df_blue_big = pd.DataFrame(blue_big_data)
            df_blue_big.to_excel(writer, sheet_name='蓝球大球数概率', index=False)
        
        # 冷球数概率
        if 'statistical' in probability_tables:
            # 红球冷球数概率
            red_cold_ball_data = []
            red_cold_probs = probability_tables['statistical'].get('red_cold_ball_probabilities', {})
            for count in range(7):
                red_cold_ball_data.append({
                    '红球冷球数': count,
                    '历史出现概率': red_cold_probs.get(count, 0)
                })

            df_red_cold_ball = pd.DataFrame(red_cold_ball_data)
            df_red_cold_ball.to_excel(writer, sheet_name='红球冷球数概率', index=False)

            # 蓝球冷球数概率
            blue_cold_ball_data = []
            blue_cold_probs = probability_tables['statistical'].get('blue_cold_ball_probabilities', {})
            for count in range(2):  # 0-1个冷球
                blue_cold_ball_data.append({
                    '蓝球冷球数': count,
                    '历史出现概率': blue_cold_probs.get(count, 0)
                })

            df_blue_cold_ball = pd.DataFrame(blue_cold_ball_data)
            df_blue_cold_ball.to_excel(writer, sheet_name='蓝球冷球数概率', index=False)
        
        # 重号数概率
        if 'statistical' in probability_tables:
            repeat_data = []
            
            repeat_probs = probability_tables['statistical'].get('red_repeat_probabilities', {})
            for count in range(7):
                repeat_data.append({
                    '红球重号数': count,
                    '历史出现概率': repeat_probs.get(count, 0)
                })
            
            df_repeat = pd.DataFrame(repeat_data)
            df_repeat.to_excel(writer, sheet_name='红球重号数概率', index=False)
        
        # 马尔科夫链概率
        if 'markov' in probability_tables:
            markov_red_data = []
            markov_red_probs = probability_tables['markov'].get('red_markov_probabilities', {})
            for ball in range(1, 34):
                markov_red_data.append({
                    '红球号码': ball,
                    '马尔科夫链概率': markov_red_probs.get(ball, 0)
                })
            
            df_markov_red = pd.DataFrame(markov_red_data)
            df_markov_red.to_excel(writer, sheet_name='红球马尔科夫概率', index=False)
            
            markov_blue_data = []
            markov_blue_probs = probability_tables['markov'].get('blue_markov_probabilities', {})
            for ball in range(1, 17):
                markov_blue_data.append({
                    '蓝球号码': ball,
                    '马尔科夫链概率': markov_blue_probs.get(ball, 0)
                })
            
            df_markov_blue = pd.DataFrame(markov_blue_data)
            df_markov_blue.to_excel(writer, sheet_name='蓝球马尔科夫概率', index=False)
        
        # 贝叶斯概率
        if 'bayesian' in probability_tables:
            bayesian_red_data = []
            bayesian_red_probs = probability_tables['bayesian'].get('red_bayesian_probabilities', {})
            for ball in range(1, 34):
                bayesian_red_data.append({
                    '红球号码': ball,
                    '贝叶斯概率': bayesian_red_probs.get(ball, 0)
                })
            
            df_bayesian_red = pd.DataFrame(bayesian_red_data)
            df_bayesian_red.to_excel(writer, sheet_name='红球贝叶斯概率', index=False)
            
            bayesian_blue_data = []
            bayesian_blue_probs = probability_tables['bayesian'].get('blue_bayesian_probabilities', {})
            for ball in range(1, 17):
                bayesian_blue_data.append({
                    '蓝球号码': ball,
                    '贝叶斯概率': bayesian_blue_probs.get(ball, 0)
                })
            
            df_bayesian_blue = pd.DataFrame(bayesian_blue_data)
            df_bayesian_blue.to_excel(writer, sheet_name='蓝球贝叶斯概率', index=False)

        # 跟随性概率矩阵
        self._export_follow_matrices(probability_tables, writer)
    
    def _export_database_info(self, database, writer):
        """导出数据库信息"""
        # 修正起始和结束期号显示 - 确保按正确顺序显示
        start_period = min(database['NO'])
        end_period = max(database['NO'])

        info_data = [
            {'项目': '数据库总期数', '值': len(database)},
            {'项目': '起始期号', '值': start_period},
            {'项目': '结束期号', '值': end_period},
            {'项目': '导出时间', '值': datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
        ]

        df_info = pd.DataFrame(info_data)
        df_info.to_excel(writer, sheet_name='数据库信息', index=False)
    
    def _export_analysis_summary(self, results: List[Dict], writer):
        """导出分析摘要"""
        summary_data = []
        
        for result in results:
            period = result['period']
            database_size = result['database_size']
            latest_period = result['latest_period']
            comparison = result['comparison']
            
            # 计算该期的最佳预测
            best_hit = 0
            best_groups = []
            
            for group_id, group_result in comparison.items():
                max_hits = group_result['max_hit']['total_hits']
                if max_hits > best_hit:
                    best_hit = max_hits
                    best_groups = [group_id]
                elif max_hits == best_hit:
                    best_groups.append(group_id)
            
            red_balls_str = ' '.join(map(str, latest_period['red_balls']))
            best_groups_str = ', '.join(map(str, best_groups))
            
            summary_data.append({
                '分析期号': period,
                '数据库期数': database_size,
                '最新期号码': f"{red_balls_str} + {latest_period['blue_ball']}",
                '最大命中球数': best_hit,
                '最佳预测组': best_groups_str
            })
        
        df_summary = pd.DataFrame(summary_data)
        df_summary.to_excel(writer, sheet_name='分析摘要', index=False)

    def _export_detailed_comparison(self, results: List[Dict], writer):
        """导出详细比对结果"""
        detailed_data = []

        for result in results:
            period = result['period']
            predictions = result['predictions']
            comparison = result['comparison']

            for group_id in range(1, 25):
                if group_id in predictions and group_id in comparison:
                    group_result = comparison[group_id]
                    prediction = group_result['prediction']  # 使用比对结果中的预测数据，避免引用问题
                    max_hit = group_result['max_hit']

                    red_balls_str = ' '.join(map(str, prediction['red_balls']))

                    detailed_data.append({
                        '分析期号': period,
                        '预测组号': group_id,
                        '预测方法': prediction['method'],
                        '预测红球': red_balls_str,
                        '预测蓝球': prediction['blue_ball'],
                        '最大命中球数': max_hit['total_hits'],
                        '最大命中期号': max_hit['period'],
                        '红球命中数': max_hit['red_hits'],
                        '蓝球命中数': max_hit['blue_hits'],
                        '蓝球命中状态': '是' if max_hit['blue_hits'] == 1 else '否'
                    })

        df_detailed = pd.DataFrame(detailed_data)
        df_detailed.to_excel(writer, sheet_name='详细比对结果', index=False)

    def _export_statistical_analysis(self, results: List[Dict], writer):
        """导出统计分析"""
        # 计算每组预测的命中分布统计
        hit_distribution = {}
        for group_id in range(1, 25):
            hit_distribution[group_id] = {hits: 0 for hits in range(8)}

        method_names = {}

        for result in results:
            predictions = result['predictions']
            comparison = result['comparison']

            for group_id in range(1, 25):
                if group_id in predictions and group_id in comparison:
                    # 记录方法名（使用比对结果中的预测数据）
                    if group_id not in method_names:
                        method_names[group_id] = comparison[group_id]['prediction']['method']

                    # 统计命中分布
                    max_hits = comparison[group_id]['max_hit']['total_hits']
                    hit_distribution[group_id][max_hits] += 1

        # 创建统计数据
        stats_data = []
        for group_id in range(1, 25):
            if group_id in hit_distribution:
                method_name = method_names.get(group_id, '未知方法')
                dist = hit_distribution[group_id]
                total_tests = sum(dist.values())

                # 计算平均命中数
                avg_hits = sum(hits * count for hits, count in dist.items()) / total_tests if total_tests > 0 else 0

                # 计算命中率
                hit_rates = {}
                for hits in range(8):
                    hit_rates[f'{hits}球命中率'] = (dist[hits] / total_tests * 100) if total_tests > 0 else 0

                stats_row = {
                    '预测组号': group_id,
                    '预测方法': method_name,
                    '总测试次数': total_tests,
                    '平均命中球数': round(avg_hits, 2),
                    **{f'{hits}球命中次数': dist[hits] for hits in range(8)},
                    **{f'{hits}球命中率(%)': round(hit_rates[f'{hits}球命中率'], 1) for hits in range(8)}
                }

                stats_data.append(stats_row)

        df_stats = pd.DataFrame(stats_data)
        df_stats.to_excel(writer, sheet_name='统计分析', index=False)

        # 导出方法类型统计
        self._export_method_type_analysis(results, writer)

    def _export_method_type_analysis(self, results: List[Dict], writer):
        """导出方法类型分析"""
        method_stats = {}

        for result in results:
            predictions = result['predictions']
            comparison = result['comparison']

            for group_id in range(1, 25):
                if group_id in predictions and group_id in comparison:
                    method = comparison[group_id]['prediction']['method']  # 使用比对结果中的预测数据
                    max_hits = comparison[group_id]['max_hit']['total_hits']
                    blue_hit = comparison[group_id]['max_hit']['blue_hit_status']

                    if method not in method_stats:
                        method_stats[method] = {
                            'total_hits': 0,
                            'count': 0,
                            'max_hits': 0,
                            'blue_hits': 0,
                            'hit_distribution': {i: 0 for i in range(8)}
                        }

                    method_stats[method]['total_hits'] += max_hits
                    method_stats[method]['count'] += 1
                    method_stats[method]['max_hits'] = max(method_stats[method]['max_hits'], max_hits)
                    method_stats[method]['hit_distribution'][max_hits] += 1

                    if blue_hit:
                        method_stats[method]['blue_hits'] += 1

        # 创建方法类型统计数据
        method_data = []
        for method, stats in method_stats.items():
            if stats['count'] > 0:
                avg_hits = stats['total_hits'] / stats['count']
                blue_hit_rate = stats['blue_hits'] / stats['count'] * 100

                method_row = {
                    '预测方法': method,
                    '测试次数': stats['count'],
                    '平均命中球数': round(avg_hits, 2),
                    '最大命中球数': stats['max_hits'],
                    '蓝球命中次数': stats['blue_hits'],
                    '蓝球命中率(%)': round(blue_hit_rate, 1)
                }

                # 添加命中分布
                for hits in range(8):
                    method_row[f'{hits}球命中次数'] = stats['hit_distribution'][hits]
                    hit_rate = stats['hit_distribution'][hits] / stats['count'] * 100
                    method_row[f'{hits}球命中率(%)'] = round(hit_rate, 1)

                method_data.append(method_row)

        # 按平均命中球数排序
        method_data.sort(key=lambda x: x['平均命中球数'], reverse=True)

        df_method = pd.DataFrame(method_data)
        df_method.to_excel(writer, sheet_name='方法类型分析', index=False)

    def _export_follow_matrices(self, probability_tables: Dict, writer):
        """导出跟随性概率矩阵"""
        # 红球跟随性概率矩阵
        if 'statistical' in probability_tables and 'red_follow_matrix' in probability_tables['statistical']:
            red_follow_matrix = probability_tables['statistical']['red_follow_matrix']

            if red_follow_matrix is not None:
                # 创建红球跟随性概率矩阵数据
                red_follow_data = []

                # 添加表头行
                header_row = {'当前期\\上期': '当前期\\上期'}
                for prev_ball in range(1, 34):
                    header_row[f'上期{prev_ball}'] = f'上期{prev_ball}'
                red_follow_data.append(header_row)

                # 添加数据行
                for current_ball in range(1, 34):
                    row_data = {'当前期\\上期': f'当前期{current_ball}'}
                    for prev_ball in range(1, 34):
                        # 矩阵索引是0-32，球号是1-33
                        prob_value = red_follow_matrix[current_ball - 1, prev_ball - 1]
                        row_data[f'上期{prev_ball}'] = round(prob_value, 6)
                    red_follow_data.append(row_data)

                df_red_follow = pd.DataFrame(red_follow_data)
                df_red_follow.to_excel(writer, sheet_name='红球跟随性概率矩阵', index=False)

        # 蓝球跟随性概率矩阵
        if 'statistical' in probability_tables and 'blue_follow_matrix' in probability_tables['statistical']:
            blue_follow_matrix = probability_tables['statistical']['blue_follow_matrix']

            if blue_follow_matrix is not None:
                # 创建蓝球跟随性概率矩阵数据
                blue_follow_data = []

                # 添加表头行
                header_row = {'当前期\\上期': '当前期\\上期'}
                for prev_ball in range(1, 17):
                    header_row[f'上期{prev_ball}'] = f'上期{prev_ball}'
                blue_follow_data.append(header_row)

                # 添加数据行
                for current_ball in range(1, 17):
                    row_data = {'当前期\\上期': f'当前期{current_ball}'}
                    for prev_ball in range(1, 17):
                        # 矩阵索引是0-15，球号是1-16
                        prob_value = blue_follow_matrix[current_ball - 1, prev_ball - 1]
                        row_data[f'上期{prev_ball}'] = round(prob_value, 6)
                    blue_follow_data.append(row_data)

                df_blue_follow = pd.DataFrame(blue_follow_data)
                df_blue_follow.to_excel(writer, sheet_name='蓝球跟随性概率矩阵', index=False)
