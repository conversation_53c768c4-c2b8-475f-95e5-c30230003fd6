#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的主程序
验证重号数计算是否正确
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def test_fixed_main_program():
    """测试修正后的主程序"""
    print("=== 测试修正后的主程序 ===")
    print("验证重号数计算是否正确")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 检查版本
    print(f"系统版本: 1.1 (重号数修正版)")
    
    # 用户的参数
    start_period = '25001'
    database_range = 99
    
    print(f"开始期号: {start_period}")
    print(f"数据库范围: {database_range} 期")
    
    # 直接调用主程序的分析比对方法
    print(f"\n=== 调用主程序的分析比对方法 ===")
    
    try:
        # 模拟用户输入，只运行到第50期
        analysis_periods = system.data_loader.get_analysis_periods(start_period)
        
        print(f"总分析期数: {len(analysis_periods)}")
        
        # 只运行前50期
        for i, period in enumerate(analysis_periods[:50]):
            # 获取当前数据库
            current_database = system.data_loader.get_database_for_period(
                period, database_range
            )
            
            if current_database is None or len(current_database) == 0:
                continue
            
            # 获取答案数据
            answer_data = system.data_loader.get_answer_data(period)
            
            if answer_data is None or len(answer_data) == 0:
                continue
            
            # 运行预测
            latest_period = system.data_loader.get_latest_period(current_database)
            
            # 分析
            system.statistical_analyzer.analyze(current_database)
            system.markov_analyzer.analyze(current_database, latest_period)
            system.bayesian_analyzer.analyze(current_database, latest_period)
            
            # 生成预测
            predictions = system.prediction_engine.generate_all_predictions(
                current_database, latest_period
            )
            
            # 比对结果
            comparison_result = system.comparison_engine.compare_predictions(
                predictions, answer_data
            )
            
            # 第50期显示进度
            if (i + 1) == 50:
                print(f"\n=== 第{i+1}期进度显示 ===")
                print(f"期号: {period}")
                print(f"数据库最新期: {latest_period['period']}")
                
                # 获取冷球信息
                pred_red_cold_balls, pred_blue_cold_balls = system.statistical_analyzer.get_cold_balls(latest_period)
                analysis_red_cold_balls, analysis_blue_cold_balls = system.statistical_analyzer.get_cold_balls_for_analysis(latest_period, current_database)
                
                # 获取筛选要求
                filter_requirements = system.prediction_engine.filter_requirements
                
                # 计算重号数（这里会触发调试信息）
                repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
                
                print(f"最终重号数: {repeat_count}")
                
                # 显示进度
                system.ui.show_analysis_progress(
                    i + 1, len(analysis_periods),
                    len(current_database), latest_period,
                    predictions, filter_requirements,
                    (pred_red_cold_balls, pred_blue_cold_balls), repeat_count,
                    (analysis_red_cold_balls, analysis_blue_cold_balls)
                )
                
                return repeat_count == 1
        
        return False
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("修正后主程序测试")
    print("=" * 60)
    
    success = test_fixed_main_program()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 修正后的主程序测试通过！")
        print("重号数计算正确显示为1")
        print("\n如果用户仍看到重号数为0，请：")
        print("1. 确保使用最新版本的代码")
        print("2. 重新启动程序")
        print("3. 检查是否有Python模块缓存问题")
    else:
        print("❌ 修正后的主程序测试失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
