#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重号数计算修正
验证分析比对模式中的重号数计算是否正确
"""

import sys
from modules.data_loader import DataLoader
from modules.statistical_analyzer import StatisticalAnalyzer
from modules.markov_chain import MarkovChainAnalyzer
from modules.bayesian_analyzer import BayesianAnalyzer
from modules.prediction_engine import PredictionEngine
from modules.user_interface import UserInterface


def test_repeat_count_calculation():
    """测试重号数计算"""
    print("=== 测试重号数计算修正 ===")
    
    config = {
        'red_ball_range': (1, 33),
        'blue_ball_range': (1, 16),
        'red_ball_count': 6,
        'blue_ball_count': 1,
        'red_big_ball_threshold': 16,
        'blue_big_ball_threshold': 8,
        'cold_ball_periods': 5,
        'prediction_groups': 24,
        'answer_periods': 6
    }
    
    # 加载数据
    loader = DataLoader('lottery_data_all.xlsx')
    
    # 使用特定的期号进行测试（根据用户提供的例子）
    target_period = '25050'
    database_range = 200
    
    current_database = loader.get_database_for_period(target_period, database_range)
    latest_period = loader.get_latest_period(current_database)
    
    print(f"目标期号: {target_period}")
    print(f"最新期号: {latest_period['period']}")
    
    # 显示最新期号码
    red_str = ' '.join(map(str, latest_period['red_balls']))
    print(f"最新期号码: {latest_period['period']} {red_str} + {latest_period['blue_ball']}")
    
    # 手动查找上一期数据
    current_period_num = int(latest_period['period'])
    prev_period_num = current_period_num - 1
    
    print(f"\n查找上一期: {prev_period_num}")
    
    # 在数据库中查找上一期
    prev_row = None
    for _, row in current_database.iterrows():
        if row['NO'] == prev_period_num:
            prev_row = row
            break
    
    if prev_row is not None:
        prev_red_balls = [int(prev_row[f'r{j}']) for j in range(1, 7)]
        prev_blue_ball = int(prev_row['b'])
        prev_red_str = ' '.join(map(str, prev_red_balls))
        print(f"上一期号码: {prev_period_num} {prev_red_str} + {prev_blue_ball}")
        
        # 手动计算重号数
        current_red_balls = latest_period['red_balls']
        manual_repeat_count = sum(1 for ball in current_red_balls if ball in prev_red_balls)
        
        # 显示重号球
        repeat_balls = [ball for ball in current_red_balls if ball in prev_red_balls]
        
        print(f"\n手动计算:")
        print(f"  当前期红球: {current_red_balls}")
        print(f"  上一期红球: {prev_red_balls}")
        print(f"  重号球: {repeat_balls}")
        print(f"  重号数: {manual_repeat_count}")
        
        # 验证期望结果
        expected_repeat_count = 1  # 根据用户描述，应该是1（红球33）
        if manual_repeat_count == expected_repeat_count:
            print(f"✅ 手动计算正确: {manual_repeat_count}")
        else:
            print(f"❌ 手动计算错误: 实际{manual_repeat_count}, 期望{expected_repeat_count}")
        
        return manual_repeat_count == expected_repeat_count
    else:
        print(f"❌ 未找到上一期数据: {prev_period_num}")
        return False


def test_system_repeat_count():
    """测试系统的重号数计算"""
    print(f"\n=== 测试系统重号数计算 ===")
    
    # 导入系统
    from ssq_lottery_system import SSQLotterySystem
    
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 使用特定的期号进行测试
    target_period = '25050'
    database_range = 200
    
    current_database = system.data_loader.get_database_for_period(target_period, database_range)
    latest_period = system.data_loader.get_latest_period(current_database)
    
    print(f"测试期号: {latest_period['period']}")
    
    # 测试原有方法
    old_repeat_count = system._calculate_repeat_count(current_database, latest_period)
    print(f"原有方法计算的重号数: {old_repeat_count}")
    
    # 测试新方法
    new_repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
    print(f"新方法计算的重号数: {new_repeat_count}")
    
    # 验证期望结果
    expected_repeat_count = 1
    
    if new_repeat_count == expected_repeat_count:
        print(f"✅ 新方法计算正确: {new_repeat_count}")
        return True
    else:
        print(f"❌ 新方法计算错误: 实际{new_repeat_count}, 期望{expected_repeat_count}")
        return False


def test_analysis_progress_display():
    """测试分析进度显示中的重号数"""
    print(f"\n=== 测试分析进度显示中的重号数 ===")
    
    from ssq_lottery_system import SSQLotterySystem
    
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 使用特定的期号进行测试
    target_period = '25050'
    database_range = 200
    
    current_database = system.data_loader.get_database_for_period(target_period, database_range)
    latest_period = system.data_loader.get_latest_period(current_database)
    
    # 运行分析
    system.statistical_analyzer.analyze(current_database)
    system.markov_analyzer.analyze(current_database, latest_period)
    system.bayesian_analyzer.analyze(current_database, latest_period)
    
    # 生成预测
    predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
    
    # 获取冷球信息
    pred_red_cold_balls, pred_blue_cold_balls = system.statistical_analyzer.get_cold_balls(latest_period)
    analysis_red_cold_balls, analysis_blue_cold_balls = system.statistical_analyzer.get_cold_balls_for_analysis(latest_period, current_database)
    
    # 获取筛选要求
    filter_requirements = system.prediction_engine.filter_requirements
    
    # 计算重号数
    repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
    
    print(f"计算的重号数: {repeat_count}")
    
    # 显示进度信息
    print(f"\n模拟分析进度显示:")
    system.ui.show_analysis_progress(
        50, 100,  # 假设完成50/100期
        len(current_database), latest_period,
        predictions, filter_requirements,
        (pred_red_cold_balls, pred_blue_cold_balls), repeat_count,
        (analysis_red_cold_balls, analysis_blue_cold_balls)
    )
    
    # 验证期望结果
    expected_repeat_count = 1
    
    if repeat_count == expected_repeat_count:
        print(f"\n✅ 分析进度显示中的重号数正确: {repeat_count}")
        return True
    else:
        print(f"\n❌ 分析进度显示中的重号数错误: 实际{repeat_count}, 期望{expected_repeat_count}")
        return False


def main():
    """主测试函数"""
    print("重号数计算修正测试")
    print("=" * 60)
    
    success1 = test_repeat_count_calculation()
    success2 = test_system_repeat_count()
    success3 = test_analysis_progress_display()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3:
        print("🎉 重号数计算修正测试通过！")
        return True
    else:
        print("⚠️ 重号数计算仍需修正")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
