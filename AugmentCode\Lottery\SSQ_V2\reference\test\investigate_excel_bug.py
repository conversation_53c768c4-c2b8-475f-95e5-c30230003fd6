#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调查Excel bug
分析为什么会出现错误的比对结果
"""

import sys
import pandas as pd
from modules.comparison_engine import ComparisonEngine


def investigate_excel_bug():
    """调查Excel bug"""
    print("=== 调查Excel bug ===")
    print("分析为什么会出现错误的比对结果")
    
    # 模拟用户Excel中显示的错误数据
    print("\n用户Excel显示的错误数据:")
    excel_data = [
        {'组号': 2, '预测': '[2, 7, 10, 22, 27, 33] + 14', '最大命中球数': 5, '红球命中数': 4, '蓝球命中数': 1, '蓝球命中状态': '是'},
        {'组号': 6, '预测': '[2, 7, 10, 22, 27, 33] + 14', '最大命中球数': 5, '红球命中数': 5, '蓝球命中数': 1, '蓝球命中状态': '是'},
        {'组号': 9, '预测': '[2, 7, 10, 22, 27, 33] + 14', '最大命中球数': 6, '红球命中数': 5, '蓝球命中数': 1, '蓝球命中状态': '是'},
        {'组号': 18, '预测': '[2, 7, 10, 22, 27, 33] + 14', '最大命中球数': 6, '红球命中数': 5, '蓝球命中数': 1, '蓝球命中状态': '是'}
    ]
    
    for data in excel_data:
        print(f"  第{data['组号']}组: 最大命中{data['最大命中球数']}球, 红{data['红球命中数']}+蓝{data['蓝球命中数']}, 蓝球状态{data['蓝球命中状态']}")
    
    # 正确的计算结果
    print(f"\n正确的计算结果:")
    print(f"  预测: [2, 7, 10, 22, 27, 33] + 14")
    print(f"  实际: [2, 7, 10, 27, 30, 33] + 11 (25073期)")
    print(f"  红球命中: {2, 7, 10, 27, 33} = 5个")
    print(f"  蓝球命中: 14 ≠ 11 = 0个")
    print(f"  总命中: 5球")
    print(f"  蓝球状态: 否")
    
    # 分析错误的可能原因
    print(f"\n=== 分析错误的可能原因 ===")
    
    # 1. 检查是否是数据类型问题
    print("1. 检查数据类型问题:")
    test_prediction = {
        'red_balls': [2, 7, 10, 22, 27, 33],
        'blue_ball': 14,
        'method': '测试'
    }
    
    test_answer = {
        'period': '25073',
        'red_balls': [2, 7, 10, 27, 30, 33],
        'blue_ball': 11
    }
    
    # 手动计算
    pred_red_set = set(test_prediction['red_balls'])
    answer_red_set = set(test_answer['red_balls'])
    red_hits = len(pred_red_set & answer_red_set)
    blue_hits = 1 if test_prediction['blue_ball'] == test_answer['blue_ball'] else 0
    total_hits = red_hits + blue_hits
    
    print(f"  手动计算: 红{red_hits}+蓝{blue_hits}={total_hits}球")
    
    # 2. 检查是否是比对引擎问题
    print("\n2. 检查比对引擎:")
    config = {}
    engine = ComparisonEngine(config)
    
    results = engine._compare_single_prediction(test_prediction, [test_answer])
    if results:
        result = results[0]
        print(f"  引擎计算: 红{result['red_hits']}+蓝{result['blue_hits']}={result['total_hits']}球")
        print(f"  蓝球状态: {result['blue_hit_status']}")
    
    # 3. 检查可能的数据混淆
    print("\n3. 检查可能的数据混淆:")
    
    # 假设错误地使用了不同的实际号码
    wrong_answers = [
        {'period': '25073', 'red_balls': [2, 7, 10, 22, 30, 33], 'blue_ball': 14},  # 错误1：蓝球相同
        {'period': '25073', 'red_balls': [2, 7, 10, 30, 33], 'blue_ball': 11},      # 错误2：少了一个红球
        {'period': '25073', 'red_balls': [2, 7, 10, 22, 27, 30, 33], 'blue_ball': 11}  # 错误3：多了一个红球
    ]
    
    for i, wrong_answer in enumerate(wrong_answers):
        print(f"\n  错误假设{i+1}: {wrong_answer['red_balls']} + {wrong_answer['blue_ball']}")
        
        wrong_results = engine._compare_single_prediction(test_prediction, [wrong_answer])
        if wrong_results:
            wrong_result = wrong_results[0]
            print(f"    结果: 红{wrong_result['red_hits']}+蓝{wrong_result['blue_hits']}={wrong_result['total_hits']}球")
            print(f"    蓝球状态: {wrong_result['blue_hit_status']}")
            
            # 检查是否匹配Excel中的错误数据
            for excel_row in excel_data:
                if (wrong_result['red_hits'] == excel_row['红球命中数'] and
                    wrong_result['blue_hits'] == excel_row['蓝球命中数'] and
                    wrong_result['total_hits'] == excel_row['最大命中球数']):
                    print(f"    ⚠️ 匹配Excel第{excel_row['组号']}组的错误数据！")
    
    return False  # 因为发现了错误


def check_excel_export_logic():
    """检查Excel导出逻辑"""
    print(f"\n=== 检查Excel导出逻辑 ===")
    
    # 检查导出管理器的代码
    from modules.export_manager import ExportManager
    
    print("检查Excel导出逻辑中可能的问题:")
    print("1. 数据传递过程中的类型转换")
    print("2. 最大命中选择逻辑")
    print("3. 蓝球命中状态的计算")
    
    # 模拟导出数据
    mock_result = {
        'period': '25067',
        'predictions': {
            1: {
                'red_balls': [2, 7, 10, 22, 27, 33],
                'blue_ball': 14,
                'method': '测试方法'
            }
        },
        'comparison': {
            1: {
                'max_hit': {
                    'period': '25073',
                    'red_hits': 5,
                    'blue_hits': 0,
                    'total_hits': 5,
                    'blue_hit_status': False,
                    'answer_red_balls': [2, 7, 10, 27, 30, 33],
                    'answer_blue_ball': 11
                }
            }
        }
    }
    
    print(f"\n模拟正确的导出数据:")
    period = mock_result['period']
    predictions = mock_result['predictions']
    comparison = mock_result['comparison']
    
    for group_id in [1]:
        if group_id in predictions and group_id in comparison:
            prediction = predictions[group_id]
            group_result = comparison[group_id]
            max_hit = group_result['max_hit']
            
            red_balls_str = ' '.join(map(str, prediction['red_balls']))
            
            export_data = {
                '分析期号': period,
                '预测组号': group_id,
                '预测方法': prediction['method'],
                '预测红球': red_balls_str,
                '预测蓝球': prediction['blue_ball'],
                '最大命中球数': max_hit['total_hits'],
                '最大命中期号': max_hit['period'],
                '红球命中数': max_hit['red_hits'],
                '蓝球命中数': max_hit['blue_hits'],
                '蓝球命中状态': '是' if max_hit['blue_hits'] == 1 else '否'
            }
            
            print(f"  第{group_id}组导出数据:")
            for key, value in export_data.items():
                print(f"    {key}: {value}")
            
            # 验证正确性
            if (export_data['最大命中球数'] == 5 and
                export_data['红球命中数'] == 5 and
                export_data['蓝球命中数'] == 0 and
                export_data['蓝球命中状态'] == '否'):
                print(f"    ✅ 导出数据正确")
                return True
            else:
                print(f"    ❌ 导出数据错误")
                return False
    
    return False


def main():
    """主函数"""
    print("调查Excel bug")
    print("=" * 60)
    
    success1 = investigate_excel_bug()
    success2 = check_excel_export_logic()
    
    print("\n" + "=" * 60)
    print("🔍 调查结论:")
    print("1. 比对引擎本身的计算逻辑是正确的")
    print("2. 用户Excel中显示的预测号码在当前系统中不存在")
    print("3. Excel中的错误数据可能来源于:")
    print("   - 旧版本的预测算法")
    print("   - 数据传递过程中的错误")
    print("   - 手动修改的数据")
    print("   - 不同参数下的预测结果")
    print("\n建议:")
    print("1. 重新运行主程序生成最新的Excel文件")
    print("2. 检查Excel文件的生成时间和参数")
    print("3. 验证具体的预测组号和方法名称")
    
    return success1 or success2


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
