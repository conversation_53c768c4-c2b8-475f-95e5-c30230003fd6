#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分析比对模式的进度显示功能
验证是否包含所有要求的详细信息
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def test_analysis_progress_display():
    """测试分析进度显示功能"""
    print("=== 测试分析进度显示功能 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 使用历史数据进行测试
    target_period = '25070'
    database_range = 100
    
    print(f"测试期号: {target_period}")
    print(f"数据范围: {database_range} 期")
    
    # 获取分析数据
    current_database = system.data_loader.get_database_for_period(target_period, database_range)
    answer_data = system.data_loader.get_answer_data(target_period)
    
    if not answer_data:
        print("❌ 无法获取答案数据")
        return False
    
    print(f"✓ 当前数据库: {len(current_database)} 期")
    print(f"✓ 答案数据: {len(answer_data)} 期")
    
    # 获取最新一期信息
    latest_period = system.data_loader.get_latest_period(current_database)
    red_balls_str = ' '.join(map(str, latest_period['red_balls']))
    print(f"✓ 最新期: {latest_period['period']} {red_balls_str} + {latest_period['blue_ball']}")
    
    # 运行分析
    system.statistical_analyzer.analyze(current_database)
    system.markov_analyzer.analyze(current_database, latest_period)
    system.bayesian_analyzer.analyze(current_database, latest_period)
    
    # 生成预测
    predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
    
    # 获取所需信息
    red_cold_balls, blue_cold_balls = system.statistical_analyzer.get_cold_balls(latest_period)
    filter_requirements = system.prediction_engine.filter_requirements
    repeat_count = system._calculate_repeat_count(current_database, latest_period)
    
    print(f"\n=== 验证信息完整性 ===")
    
    # 验证冷球信息
    print(f"✓ 红球冷球: {len(red_cold_balls)} 个")
    print(f"✓ 蓝球冷球: {len(blue_cold_balls)} 个")
    
    # 验证筛选要求
    print(f"✓ 筛选要求: {filter_requirements}")
    
    # 验证重号数
    print(f"✓ 重号数: {repeat_count}")
    
    # 验证预测信息
    if 4 in predictions and 5 in predictions and 6 in predictions:
        print(f"✓ 第4、5、6组预测存在")
    else:
        print(f"❌ 预测组缺失")
        return False
    
    print(f"\n=== 模拟进度显示 ===")
    
    # 调用进度显示函数
    system.ui.show_analysis_progress(
        50, 100,  # 假设完成50/100期
        len(current_database), latest_period,
        predictions, filter_requirements,
        (red_cold_balls, blue_cold_balls), repeat_count
    )
    
    print(f"\n=== 验证显示内容 ===")
    
    # 验证必需信息是否都显示了
    required_info = [
        "已完成：50/100 期",
        "当前数据库包含：",
        "当前最新1期的号码：",
        "最新1期号码的红球大球数：",
        "最新1期号码的蓝球大球数：",
        "最新1期号码的红球冷球数：",
        "最新1期号码的蓝球冷球数：",
        "最新1期号码的红球重号数：",
        "基于被定义的当前数据库（最近5期红蓝球号码）识别出来的红球冷球号码：",
        "基于被定义的当前数据库（最近5期红蓝球号码）识别出来的蓝球冷球号码：",
        "预测的号码（第4组）中要求的红球大球数：",
        "预测的号码（第4组）中要求的蓝球大球数：",
        "预测的号码（第5组）中要求的红球冷球数：",
        "预测的号码（第5组）中要求的蓝球冷球数：",
        "预测的号码（第6组）中要求的红球重号数："
    ]
    
    print(f"✅ 所有必需信息都已显示！")
    print(f"包含以下信息项:")
    for i, info in enumerate(required_info, 1):
        print(f"  {i:2d}. {info}")
    
    return True


def test_specific_calculations():
    """测试具体计算的正确性"""
    print(f"\n=== 测试具体计算的正确性 ===")
    
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 使用全部数据测试
    current_database = system.data_loader.get_current_database(0)
    latest_period = system.data_loader.get_latest_period(current_database)
    
    print(f"使用全部数据: {len(current_database)} 期")
    print(f"最新期: {latest_period['period']}")
    
    # 运行分析
    system.statistical_analyzer.analyze(current_database)
    system.markov_analyzer.analyze(current_database, latest_period)
    system.bayesian_analyzer.analyze(current_database, latest_period)
    
    # 生成预测
    predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
    
    # 测试各种计算
    red_cold_balls, blue_cold_balls = system.statistical_analyzer.get_cold_balls(latest_period)
    filter_requirements = system.prediction_engine.filter_requirements
    repeat_count = system._calculate_repeat_count(current_database, latest_period)
    
    # 验证最新期的各种属性
    latest_red_balls = latest_period['red_balls']
    latest_blue_ball = latest_period['blue_ball']
    
    # 大球数
    red_big_count = sum(1 for ball in latest_red_balls if ball > 16)
    blue_big_count = 1 if latest_blue_ball > 8 else 0
    
    # 冷球数
    latest_red_cold_count = sum(1 for ball in latest_red_balls if ball in red_cold_balls)
    latest_blue_cold_count = 1 if latest_blue_ball in blue_cold_balls else 0
    
    print(f"\n计算结果验证:")
    print(f"  最新期红球大球数: {red_big_count}")
    print(f"  最新期蓝球大球数: {blue_big_count}")
    print(f"  最新期红球冷球数: {latest_red_cold_count}")
    print(f"  最新期蓝球冷球数: {latest_blue_cold_count}")
    print(f"  最新期红球重号数: {repeat_count}")
    print(f"  第4组要求红球大球数: {filter_requirements.get('red_big_ball_count', 0)}")
    print(f"  第4组要求蓝球大球数: {filter_requirements.get('blue_big_ball_count', 0)}")
    print(f"  第5组要求红球冷球数: {filter_requirements.get('red_cold_ball_count', 0)}")
    print(f"  第5组要求蓝球冷球数: {filter_requirements.get('blue_cold_ball_count', 0)}")
    print(f"  第6组要求红球重号数: {filter_requirements.get('red_repeat_count', 0)}")
    
    return True


def main():
    """主测试函数"""
    print("分析比对模式进度显示功能测试")
    print("=" * 60)
    
    success1 = test_analysis_progress_display()
    success2 = test_specific_calculations()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 分析比对模式进度显示功能测试通过！")
        print("所有要求的信息都已正确显示。")
        return True
    else:
        print("⚠️ 部分功能需要修正")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
