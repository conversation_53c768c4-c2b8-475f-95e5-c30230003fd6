# -*- coding: utf-8 -*-
"""
预测引擎模块 (Prediction Engine Module)

负责实现24种预测算法，结合基础算法和各种筛选策略。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from collections import Counter
from .prediction_helpers import PredictionHelpers
from .constraint_helpers import ConstraintHelpers


class PredictionEngine:
    """
    预测引擎类
    
    负责生成所有24组预测号码
    """
    
    def __init__(self, config: Dict, statistical_analyzer, markov_analyzer, bayesian_analyzer):
        """
        初始化预测引擎
        
        Args:
            config: 系统配置字典
            statistical_analyzer: 统计分析器
            markov_analyzer: 马尔科夫链分析器
            bayesian_analyzer: 贝叶斯分析器
        """
        self.config = config
        self.statistical_analyzer = statistical_analyzer
        self.markov_analyzer = markov_analyzer
        self.bayesian_analyzer = bayesian_analyzer
        
        self.database = None
        self.latest_period = None
        
        # 预测结果存储
        self.predictions = {}
        
        # 筛选要求存储
        self.filter_requirements = {}
    
    def generate_all_predictions(self, database: pd.DataFrame, latest_period: Dict) -> Dict:
        """
        生成所有24组预测号码
        
        Args:
            database: 数据库DataFrame
            latest_period: 最新一期数据
            
        Returns:
            预测结果字典
        """
        self.database = database
        self.latest_period = latest_period
        
        # 获取筛选要求
        self._calculate_filter_requirements()
        
        # 生成基础预测（第1-3组）
        self._generate_basic_predictions()
        
        # 生成单一筛选预测（第4-6组）
        self._generate_single_filter_predictions()
        
        # 生成组合筛选预测（第7-24组）
        self._generate_combined_filter_predictions()
        
        return self.predictions
    
    def _calculate_filter_requirements(self):
        """计算各种筛选要求"""
        # 获取概率最大的各项数值
        most_probable = self.statistical_analyzer.get_most_probable_values()
        
        self.filter_requirements = {
            'red_big_ball_count': most_probable.get('red_big_ball_count', 3),
            'blue_big_ball_count': most_probable.get('blue_big_ball_count', 0),
            'red_cold_ball_count': most_probable.get('red_cold_ball_count', 2),
            'blue_cold_ball_count': most_probable.get('blue_cold_ball_count', 0),
            'red_repeat_count': most_probable.get('red_repeat_count', 1)
        }
    
    def _generate_basic_predictions(self):
        """生成基础预测（第1-3组）"""
        # 第1组：历史出现概率
        self.predictions[1] = self._predict_by_historical_probability()
        
        # 第2组：马尔科夫链
        self.predictions[2] = self._predict_by_markov_chain()
        
        # 第3组：贝叶斯概率
        self.predictions[3] = self._predict_by_bayesian_probability()
    
    def _generate_single_filter_predictions(self):
        """生成单一筛选预测（第4-6组）"""
        # 第4组：历史出现概率 + 大球筛选
        self.predictions[4] = self._predict_with_big_ball_filter(
            self.predictions[1], 'historical'
        )
        
        # 第5组：历史出现概率 + 冷球筛选
        self.predictions[5] = self._predict_with_cold_ball_filter(
            self.predictions[1], 'historical'
        )
        
        # 第6组：历史出现概率 + 红球重号筛选
        self.predictions[6] = self._predict_with_repeat_filter(
            self.predictions[1], 'historical'
        )
    
    def _generate_combined_filter_predictions(self):
        """生成组合筛选预测（第7-24组）"""
        # 第7-9组：马尔科夫链 + 单一筛选
        self.predictions[7] = self._predict_with_big_ball_filter(
            self.predictions[2], 'markov'
        )
        self.predictions[8] = self._predict_with_cold_ball_filter(
            self.predictions[2], 'markov'
        )
        self.predictions[9] = self._predict_with_repeat_filter(
            self.predictions[2], 'markov'
        )
        
        # 第10-12组：贝叶斯概率 + 单一筛选
        self.predictions[10] = self._predict_with_big_ball_filter(
            self.predictions[3], 'bayesian'
        )
        self.predictions[11] = self._predict_with_cold_ball_filter(
            self.predictions[3], 'bayesian'
        )
        self.predictions[12] = self._predict_with_repeat_filter(
            self.predictions[3], 'bayesian'
        )
        
        # 第13-16组：历史出现概率 + 组合筛选
        self.predictions[13] = self._predict_with_big_cold_filter(
            self.predictions[4]
        )
        self.predictions[14] = self._predict_with_big_repeat_filter(
            self.predictions[4]
        )
        self.predictions[15] = self._predict_with_cold_repeat_filter(
            self.predictions[5]
        )
        self.predictions[16] = self._predict_with_all_filters(
            self.predictions[13]
        )
        
        # 第17-20组：马尔科夫链 + 组合筛选
        self.predictions[17] = self._predict_with_big_cold_filter(
            self.predictions[7]
        )
        self.predictions[18] = self._predict_with_big_repeat_filter(
            self.predictions[7]
        )
        self.predictions[19] = self._predict_with_cold_repeat_filter(
            self.predictions[8]
        )
        self.predictions[20] = self._predict_with_all_filters(
            self.predictions[17]
        )
        
        # 第21-24组：贝叶斯概率 + 组合筛选
        self.predictions[21] = self._predict_with_big_cold_filter(
            self.predictions[10]
        )
        self.predictions[22] = self._predict_with_big_repeat_filter(
            self.predictions[10]
        )
        self.predictions[23] = self._predict_with_cold_repeat_filter(
            self.predictions[11]
        )
        self.predictions[24] = self._predict_with_all_filters(
            self.predictions[21]
        )
    
    def _predict_by_historical_probability(self) -> Dict:
        """第1组：历史出现概率预测"""
        # 获取红球概率最高的6个号码
        red_probs = self.statistical_analyzer.red_ball_probabilities
        sorted_red = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
        red_balls = sorted([ball for ball, _ in sorted_red[:6]])
        
        # 获取蓝球概率最高的1个号码
        blue_probs = self.statistical_analyzer.blue_ball_probabilities
        blue_ball = max(blue_probs.items(), key=lambda x: x[1])[0]
        
        return {
            'method': '历史出现概率',
            'red_balls': red_balls,
            'blue_ball': blue_ball
        }
    
    def _predict_by_markov_chain(self) -> Dict:
        """第2组：马尔科夫链预测"""
        # 获取马尔科夫链概率最高的号码
        red_balls, _ = self.markov_analyzer.get_top_probabilities(6)
        blue_ball, _ = self.markov_analyzer.get_top_blue_probability()
        
        return {
            'method': '马尔科夫链',
            'red_balls': sorted(red_balls),
            'blue_ball': blue_ball
        }
    
    def _predict_by_bayesian_probability(self) -> Dict:
        """第3组：贝叶斯概率预测"""
        # 获取贝叶斯概率最高的号码
        red_balls, _ = self.bayesian_analyzer.get_top_probabilities(6)
        blue_ball, _ = self.bayesian_analyzer.get_top_blue_probability()
        
        return {
            'method': '贝叶斯概率',
            'red_balls': sorted(red_balls),
            'blue_ball': blue_ball
        }
    
    def _predict_with_big_ball_filter(self, base_prediction: Dict, method_type: str) -> Dict:
        """应用大球筛选"""
        red_balls = base_prediction['red_balls'].copy()
        blue_ball = base_prediction['blue_ball']
        
        # 调整红球大球数
        red_balls = self._adjust_red_big_balls(
            red_balls, 
            self.filter_requirements['red_big_ball_count'],
            method_type
        )
        
        # 调整蓝球大球数
        blue_ball = self._adjust_blue_big_ball(
            blue_ball,
            self.filter_requirements['blue_big_ball_count'],
            method_type
        )
        
        method_name = f"{base_prediction['method']}+大球筛选"
        
        return {
            'method': method_name,
            'red_balls': sorted(red_balls),
            'blue_ball': blue_ball
        }
    
    def _predict_with_cold_ball_filter(self, base_prediction: Dict, method_type: str) -> Dict:
        """应用冷球筛选"""
        red_balls = base_prediction['red_balls'].copy()
        blue_ball = base_prediction['blue_ball']
        
        # 获取冷球信息
        red_cold_balls, blue_cold_balls = self.statistical_analyzer.get_cold_balls(self.latest_period)
        
        # 调整红球冷球数
        red_balls = self._adjust_red_cold_balls(
            red_balls,
            self.filter_requirements['red_cold_ball_count'],
            red_cold_balls,
            method_type
        )
        
        # 调整蓝球冷球数
        blue_ball = self._adjust_blue_cold_ball(
            blue_ball,
            self.filter_requirements['blue_cold_ball_count'],
            blue_cold_balls,
            method_type
        )
        
        method_name = f"{base_prediction['method']}+冷球筛选"
        
        return {
            'method': method_name,
            'red_balls': sorted(red_balls),
            'blue_ball': blue_ball
        }
    
    def _predict_with_repeat_filter(self, base_prediction: Dict, method_type: str) -> Dict:
        """应用重号筛选"""
        red_balls = base_prediction['red_balls'].copy()
        blue_ball = base_prediction['blue_ball']
        
        # 调整红球重号数
        red_balls = self._adjust_red_repeat_balls(
            red_balls,
            self.filter_requirements['red_repeat_count'],
            method_type
        )
        
        method_name = f"{base_prediction['method']}+红球重号筛选"
        
        return {
            'method': method_name,
            'red_balls': sorted(red_balls),
            'blue_ball': blue_ball
        }
    
    def _predict_with_big_cold_filter(self, base_prediction: Dict) -> Dict:
        """应用大球+冷球筛选"""
        # 基于大球筛选的结果，再应用冷球筛选
        red_balls = base_prediction['red_balls'].copy()
        blue_ball = base_prediction['blue_ball']
        
        # 获取冷球信息
        red_cold_balls, blue_cold_balls = self.statistical_analyzer.get_cold_balls(self.latest_period)
        
        # 调整红球冷球数（保持大球数不变）
        red_balls = self._adjust_red_cold_balls_with_big_constraint(
            red_balls,
            self.filter_requirements['red_cold_ball_count'],
            red_cold_balls,
            self.filter_requirements['red_big_ball_count']
        )
        
        # 调整蓝球冷球数（保持大球数不变）
        blue_ball = self._adjust_blue_cold_ball_with_big_constraint(
            blue_ball,
            self.filter_requirements['blue_cold_ball_count'],
            blue_cold_balls,
            self.filter_requirements['blue_big_ball_count']
        )
        
        method_name = f"{base_prediction['method'].replace('+大球筛选', '')}+大球冷球筛选"
        
        return {
            'method': method_name,
            'red_balls': sorted(red_balls),
            'blue_ball': blue_ball
        }

    def _predict_with_big_repeat_filter(self, base_prediction: Dict) -> Dict:
        """应用大球+重号筛选"""
        red_balls = base_prediction['red_balls'].copy()
        blue_ball = base_prediction['blue_ball']

        # 调整红球重号数（保持大球数不变）
        red_balls = self._adjust_red_repeat_balls_with_big_constraint(
            red_balls,
            self.filter_requirements['red_repeat_count'],
            self.filter_requirements['red_big_ball_count']
        )

        method_name = f"{base_prediction['method'].replace('+大球筛选', '')}+大球红球重号筛选"

        return {
            'method': method_name,
            'red_balls': sorted(red_balls),
            'blue_ball': blue_ball
        }

    def _predict_with_cold_repeat_filter(self, base_prediction: Dict) -> Dict:
        """应用冷球+重号筛选"""
        red_balls = base_prediction['red_balls'].copy()
        blue_ball = base_prediction['blue_ball']

        # 调整红球重号数（保持冷球数不变）
        red_balls = self._adjust_red_repeat_balls_with_cold_constraint(
            red_balls,
            self.filter_requirements['red_repeat_count'],
            self.filter_requirements['red_cold_ball_count']
        )

        method_name = f"{base_prediction['method'].replace('+冷球筛选', '')}+冷球红球重号筛选"

        return {
            'method': method_name,
            'red_balls': sorted(red_balls),
            'blue_ball': blue_ball
        }

    def _predict_with_all_filters(self, base_prediction: Dict) -> Dict:
        """应用所有筛选条件"""
        red_balls = base_prediction['red_balls'].copy()
        blue_ball = base_prediction['blue_ball']

        # 调整红球重号数（保持大球数和冷球数不变）
        red_balls = self._adjust_red_repeat_balls_with_all_constraints(
            red_balls,
            self.filter_requirements['red_repeat_count'],
            self.filter_requirements['red_big_ball_count'],
            self.filter_requirements['red_cold_ball_count']
        )

        method_name = f"{base_prediction['method'].replace('+大球冷球筛选', '')}+大球冷球红球重号筛选"

        return {
            'method': method_name,
            'red_balls': sorted(red_balls),
            'blue_ball': blue_ball
        }

    def _is_big_ball(self, ball: int, ball_type: str) -> bool:
        """判断是否为大球"""
        if ball_type == 'red':
            return ball > self.config['red_big_ball_threshold']
        else:  # blue
            return ball > self.config['blue_big_ball_threshold']

    def _is_cold_ball(self, ball: int, cold_balls: List[int]) -> bool:
        """判断是否为冷球"""
        return ball in cold_balls

    def _is_repeat_ball(self, ball: int) -> bool:
        """判断是否为重号球"""
        return ball in self.latest_period['red_balls']

    def _get_ball_probability(self, ball: int, ball_type: str, method_type: str) -> float:
        """获取球的概率"""
        if method_type == 'historical':
            if ball_type == 'red':
                return self.statistical_analyzer.red_ball_probabilities.get(ball, 0)
            else:
                return self.statistical_analyzer.blue_ball_probabilities.get(ball, 0)
        elif method_type == 'markov':
            if ball_type == 'red':
                return self.markov_analyzer.get_red_probability(ball)
            else:
                return self.markov_analyzer.get_blue_probability(ball)
        elif method_type == 'bayesian':
            if ball_type == 'red':
                return self.bayesian_analyzer.get_red_probability(ball)
            else:
                return self.bayesian_analyzer.get_blue_probability(ball)
        return 0.0

    def _adjust_red_big_balls(self, red_balls: List[int], target_count: int, method_type: str) -> List[int]:
        """调整红球大球数"""
        current_big_count = sum(1 for ball in red_balls if self._is_big_ball(ball, 'red'))

        if current_big_count == target_count:
            return red_balls

        result_balls = red_balls.copy()

        if current_big_count > target_count:
            # 需要减少大球数
            excess = current_big_count - target_count
            big_balls_in_result = [ball for ball in result_balls if self._is_big_ball(ball, 'red')]

            # 按概率从小到大排序，移除概率最小的大球
            big_balls_with_prob = [(ball, self._get_ball_probability(ball, 'red', method_type))
                                 for ball in big_balls_in_result]
            big_balls_with_prob.sort(key=lambda x: x[1])

            for i in range(excess):
                ball_to_remove = big_balls_with_prob[i][0]
                result_balls.remove(ball_to_remove)

            # 补充非大球
            all_red_balls = list(range(1, 34))
            non_big_balls = [ball for ball in all_red_balls
                           if not self._is_big_ball(ball, 'red') and ball not in result_balls]

            # 按概率从大到小排序
            non_big_balls_with_prob = [(ball, self._get_ball_probability(ball, 'red', method_type))
                                     for ball in non_big_balls]
            non_big_balls_with_prob.sort(key=lambda x: x[1], reverse=True)

            for i in range(excess):
                if i < len(non_big_balls_with_prob):
                    result_balls.append(non_big_balls_with_prob[i][0])

        elif current_big_count < target_count:
            # 需要增加大球数
            needed = target_count - current_big_count
            all_red_balls = list(range(1, 34))
            big_balls_not_in_result = [ball for ball in all_red_balls
                                     if self._is_big_ball(ball, 'red') and ball not in result_balls]

            # 按概率从大到小排序
            big_balls_with_prob = [(ball, self._get_ball_probability(ball, 'red', method_type))
                                 for ball in big_balls_not_in_result]
            big_balls_with_prob.sort(key=lambda x: x[1], reverse=True)

            # 添加概率最大的大球
            for i in range(min(needed, len(big_balls_with_prob))):
                result_balls.append(big_balls_with_prob[i][0])

            # 移除多余的非大球
            if len(result_balls) > 6:
                non_big_balls_in_result = [ball for ball in result_balls if not self._is_big_ball(ball, 'red')]
                non_big_balls_with_prob = [(ball, self._get_ball_probability(ball, 'red', method_type))
                                         for ball in non_big_balls_in_result]
                non_big_balls_with_prob.sort(key=lambda x: x[1])

                excess_count = len(result_balls) - 6
                for i in range(excess_count):
                    if i < len(non_big_balls_with_prob):
                        result_balls.remove(non_big_balls_with_prob[i][0])

        return result_balls[:6]

    def _adjust_blue_big_ball(self, blue_ball: int, target_count: int, method_type: str) -> int:
        """调整蓝球大球数"""
        return PredictionHelpers.adjust_blue_big_ball(
            blue_ball, target_count, method_type,
            self.statistical_analyzer, self.markov_analyzer, self.bayesian_analyzer, self.config
        )

    def _adjust_red_cold_balls(self, red_balls: List[int], target_count: int,
                             cold_balls: List[int], method_type: str) -> List[int]:
        """调整红球冷球数"""
        return PredictionHelpers.adjust_red_cold_balls(
            red_balls, target_count, cold_balls, method_type,
            self.statistical_analyzer, self.markov_analyzer, self.bayesian_analyzer, self.config
        )

    def _adjust_blue_cold_ball(self, blue_ball: int, target_count: int,
                             cold_balls: List[int], method_type: str) -> int:
        """调整蓝球冷球数"""
        return PredictionHelpers.adjust_blue_cold_ball(
            blue_ball, target_count, cold_balls, method_type,
            self.statistical_analyzer, self.markov_analyzer, self.bayesian_analyzer, self.config
        )

    def _adjust_red_repeat_balls(self, red_balls: List[int], target_count: int, method_type: str) -> List[int]:
        """调整红球重号数"""
        return PredictionHelpers.adjust_red_repeat_balls(
            red_balls, target_count, self.latest_period['red_balls'], method_type,
            self.statistical_analyzer, self.markov_analyzer, self.bayesian_analyzer, self.config
        )

    def _adjust_red_cold_balls_with_big_constraint(self, red_balls: List[int], target_cold_count: int,
                                                 cold_balls: List[int], target_big_count: int) -> List[int]:
        """调整红球冷球数（保持大球数不变）"""
        # 这是一个复杂的约束优化问题，需要同时满足冷球数和大球数要求
        result_balls = red_balls.copy()

        # 首先确保大球数正确
        current_big_count = sum(1 for ball in result_balls if self._is_big_ball(ball, 'red'))
        if current_big_count != target_big_count:
            result_balls = self._adjust_red_big_balls(result_balls, target_big_count, 'historical')

        # 然后在保持大球数的前提下调整冷球数
        current_cold_count = sum(1 for ball in result_balls if ball in cold_balls)

        if current_cold_count != target_cold_count:
            # 需要在保持大球数的前提下调整冷球数
            # 这里使用简化的启发式算法
            if current_cold_count > target_cold_count:
                # 减少冷球，优先移除非大球的冷球
                excess = current_cold_count - target_cold_count
                cold_balls_in_result = [ball for ball in result_balls if ball in cold_balls]
                non_big_cold_balls = [ball for ball in cold_balls_in_result if not self._is_big_ball(ball, 'red')]

                # 按概率从小到大排序
                non_big_cold_with_prob = [(ball, self._get_ball_probability(ball, 'red', 'historical'))
                                        for ball in non_big_cold_balls]
                non_big_cold_with_prob.sort(key=lambda x: x[1])

                removed_count = 0
                for ball, _ in non_big_cold_with_prob:
                    if removed_count < excess:
                        result_balls.remove(ball)
                        removed_count += 1

                # 补充非冷球非大球
                if removed_count < excess:
                    # 如果还需要移除更多，考虑大球冷球
                    big_cold_balls = [ball for ball in cold_balls_in_result if self._is_big_ball(ball, 'red')]
                    for ball in big_cold_balls:
                        if removed_count < excess:
                            result_balls.remove(ball)
                            removed_count += 1

                # 补充球
                all_red_balls = list(range(1, 34))
                available_balls = [ball for ball in all_red_balls if ball not in result_balls]

                # 优先补充非冷球，并保持大球数
                current_big_in_result = sum(1 for ball in result_balls if self._is_big_ball(ball, 'red'))
                needed_big = target_big_count - current_big_in_result
                needed_non_big = (6 - len(result_balls)) - needed_big

                # 补充大球
                big_non_cold = [ball for ball in available_balls
                              if self._is_big_ball(ball, 'red') and ball not in cold_balls]
                big_non_cold_with_prob = [(ball, self._get_ball_probability(ball, 'red', 'historical'))
                                        for ball in big_non_cold]
                big_non_cold_with_prob.sort(key=lambda x: x[1], reverse=True)

                for i in range(min(needed_big, len(big_non_cold_with_prob))):
                    result_balls.append(big_non_cold_with_prob[i][0])

                # 补充非大球
                non_big_non_cold = [ball for ball in available_balls
                                  if not self._is_big_ball(ball, 'red') and ball not in cold_balls and ball not in result_balls]
                non_big_non_cold_with_prob = [(ball, self._get_ball_probability(ball, 'red', 'historical'))
                                            for ball in non_big_non_cold]
                non_big_non_cold_with_prob.sort(key=lambda x: x[1], reverse=True)

                for i in range(min(needed_non_big, len(non_big_non_cold_with_prob))):
                    result_balls.append(non_big_non_cold_with_prob[i][0])

        return result_balls[:6]

    def _adjust_blue_cold_ball_with_big_constraint(self, blue_ball: int, target_cold_count: int,
                                                 cold_balls: List[int], target_big_count: int) -> int:
        """调整蓝球冷球数（保持大球数不变）"""
        return ConstraintHelpers.adjust_blue_cold_ball_with_big_constraint(
            blue_ball, target_cold_count, cold_balls, target_big_count, 'historical',
            self.statistical_analyzer, self.markov_analyzer, self.bayesian_analyzer, self.config
        )

    def _adjust_red_repeat_balls_with_big_constraint(self, red_balls: List[int], target_repeat_count: int,
                                                   target_big_count: int) -> List[int]:
        """调整红球重号数（保持大球数不变）"""
        return ConstraintHelpers.adjust_red_repeat_balls_with_big_constraint(
            red_balls, target_repeat_count, target_big_count, self.latest_period['red_balls'], 'historical',
            self.statistical_analyzer, self.markov_analyzer, self.bayesian_analyzer, self.config
        )

    def _adjust_red_repeat_balls_with_cold_constraint(self, red_balls: List[int], target_repeat_count: int,
                                                    target_cold_count: int) -> List[int]:
        """调整红球重号数（保持冷球数不变）"""
        # 获取冷球信息
        red_cold_balls, _ = self.statistical_analyzer.get_cold_balls(self.latest_period)

        result_balls = red_balls.copy()

        # 确保冷球数正确
        current_cold_count = sum(1 for ball in result_balls if ball in red_cold_balls)
        if current_cold_count != target_cold_count:
            result_balls = self._adjust_red_cold_balls(result_balls, target_cold_count, red_cold_balls, 'historical')

        # 在保持冷球数的前提下调整重号数
        current_repeat_count = sum(1 for ball in result_balls if ball in self.latest_period['red_balls'])

        if current_repeat_count != target_repeat_count:
            # 使用类似的约束优化逻辑
            if current_repeat_count > target_repeat_count:
                # 减少重号数，优先移除非冷球的重号
                excess = current_repeat_count - target_repeat_count
                repeat_balls_in_result = [ball for ball in result_balls if ball in self.latest_period['red_balls']]
                non_cold_repeat_balls = [ball for ball in repeat_balls_in_result if ball not in red_cold_balls]

                # 按概率从小到大排序
                non_cold_repeat_with_prob = [(ball, self._get_ball_probability(ball, 'red', 'historical'))
                                           for ball in non_cold_repeat_balls]
                non_cold_repeat_with_prob.sort(key=lambda x: x[1])

                removed_count = 0
                for ball, _ in non_cold_repeat_with_prob:
                    if removed_count < excess:
                        result_balls.remove(ball)
                        removed_count += 1

                # 补充非重号球，保持冷球数
                current_cold_in_result = sum(1 for ball in result_balls if ball in red_cold_balls)
                needed_cold = target_cold_count - current_cold_in_result
                needed_total = 6 - len(result_balls)
                needed_non_cold = needed_total - needed_cold

                all_balls = list(range(1, 34))

                # 补充冷球非重号
                cold_non_repeat = [ball for ball in all_balls
                                 if ball in red_cold_balls and ball not in self.latest_period['red_balls'] and ball not in result_balls]
                cold_non_repeat_with_prob = [(ball, self._get_ball_probability(ball, 'red', 'historical'))
                                           for ball in cold_non_repeat]
                cold_non_repeat_with_prob.sort(key=lambda x: x[1], reverse=True)

                for i in range(min(needed_cold, len(cold_non_repeat_with_prob))):
                    result_balls.append(cold_non_repeat_with_prob[i][0])

                # 补充非冷球非重号
                non_cold_non_repeat = [ball for ball in all_balls
                                     if ball not in red_cold_balls and ball not in self.latest_period['red_balls'] and ball not in result_balls]
                non_cold_non_repeat_with_prob = [(ball, self._get_ball_probability(ball, 'red', 'historical'))
                                               for ball in non_cold_non_repeat]
                non_cold_non_repeat_with_prob.sort(key=lambda x: x[1], reverse=True)

                for i in range(min(needed_non_cold, len(non_cold_non_repeat_with_prob))):
                    result_balls.append(non_cold_non_repeat_with_prob[i][0])

        return result_balls[:6]

    def _adjust_red_repeat_balls_with_all_constraints(self, red_balls: List[int], target_repeat_count: int,
                                                    target_big_count: int, target_cold_count: int) -> List[int]:
        """调整红球重号数（保持大球数和冷球数不变）"""
        # 获取冷球信息
        red_cold_balls, _ = self.statistical_analyzer.get_cold_balls(self.latest_period)

        result_balls = red_balls.copy()

        # 这是最复杂的约束优化问题，需要同时满足大球数、冷球数和重号数
        # 使用启发式算法逐步调整

        # 首先确保大球数和冷球数正确
        current_big_count = sum(1 for ball in result_balls if self._is_big_ball(ball, 'red'))
        current_cold_count = sum(1 for ball in result_balls if ball in red_cold_balls)

        if current_big_count != target_big_count or current_cold_count != target_cold_count:
            # 重新构建满足大球数和冷球数的球组合
            result_balls = self._build_balls_with_big_cold_constraints(
                target_big_count, target_cold_count, red_cold_balls
            )

        # 在保持大球数和冷球数的前提下调整重号数
        current_repeat_count = sum(1 for ball in result_balls if ball in self.latest_period['red_balls'])

        if current_repeat_count != target_repeat_count:
            # 使用复杂的约束优化算法
            result_balls = self._optimize_with_all_constraints(
                result_balls, target_repeat_count, target_big_count, target_cold_count, red_cold_balls
            )

        return result_balls[:6]

    def _build_balls_with_big_cold_constraints(self, target_big_count: int, target_cold_count: int,
                                             red_cold_balls: List[int]) -> List[int]:
        """构建满足大球数和冷球数约束的球组合"""
        result_balls = []
        all_balls = list(range(1, 34))

        # 按概率排序所有球
        all_balls_with_prob = [(ball, self._get_ball_probability(ball, 'red', 'historical'))
                             for ball in all_balls]
        all_balls_with_prob.sort(key=lambda x: x[1], reverse=True)

        # 分类球
        big_cold_balls = [ball for ball in all_balls if self._is_big_ball(ball, 'red') and ball in red_cold_balls]
        big_non_cold_balls = [ball for ball in all_balls if self._is_big_ball(ball, 'red') and ball not in red_cold_balls]
        non_big_cold_balls = [ball for ball in all_balls if not self._is_big_ball(ball, 'red') and ball in red_cold_balls]
        non_big_non_cold_balls = [ball for ball in all_balls if not self._is_big_ball(ball, 'red') and ball not in red_cold_balls]

        # 按概率排序各类球
        for ball_list in [big_cold_balls, big_non_cold_balls, non_big_cold_balls, non_big_non_cold_balls]:
            ball_list.sort(key=lambda x: self._get_ball_probability(x, 'red', 'historical'), reverse=True)

        # 优先选择大球冷球
        big_cold_needed = min(target_big_count, target_cold_count, len(big_cold_balls))
        result_balls.extend(big_cold_balls[:big_cold_needed])

        # 补充剩余的大球
        remaining_big_needed = target_big_count - big_cold_needed
        result_balls.extend(big_non_cold_balls[:remaining_big_needed])

        # 补充剩余的冷球
        remaining_cold_needed = target_cold_count - big_cold_needed
        result_balls.extend(non_big_cold_balls[:remaining_cold_needed])

        # 补充剩余的球
        remaining_needed = 6 - len(result_balls)
        result_balls.extend(non_big_non_cold_balls[:remaining_needed])

        return result_balls[:6]

    def _optimize_with_all_constraints(self, red_balls: List[int], target_repeat_count: int,
                                     target_big_count: int, target_cold_count: int,
                                     red_cold_balls: List[int]) -> List[int]:
        """在所有约束条件下优化重号数"""
        result_balls = red_balls.copy()
        current_repeat_count = sum(1 for ball in result_balls if ball in self.latest_period['red_balls'])

        if current_repeat_count == target_repeat_count:
            return result_balls

        # 这是一个复杂的组合优化问题
        # 使用贪心算法尝试找到最优解

        if current_repeat_count > target_repeat_count:
            # 需要减少重号数
            excess = current_repeat_count - target_repeat_count
            repeat_balls_in_result = [ball for ball in result_balls if ball in self.latest_period['red_balls']]

            # 尝试移除重号球，同时保持其他约束
            for ball in repeat_balls_in_result:
                if excess <= 0:
                    break

                # 检查移除这个球后是否还能满足约束
                temp_result = [b for b in result_balls if b != ball]
                temp_big_count = sum(1 for b in temp_result if self._is_big_ball(b, 'red'))
                temp_cold_count = sum(1 for b in temp_result if b in red_cold_balls)

                # 寻找替换球
                all_balls = list(range(1, 34))
                candidates = []

                for candidate in all_balls:
                    if candidate in temp_result or candidate in self.latest_period['red_balls']:
                        continue

                    test_result = temp_result + [candidate]
                    test_big_count = sum(1 for b in test_result if self._is_big_ball(b, 'red'))
                    test_cold_count = sum(1 for b in test_result if b in red_cold_balls)

                    if test_big_count == target_big_count and test_cold_count == target_cold_count:
                        candidates.append((candidate, self._get_ball_probability(candidate, 'red', 'historical')))

                if candidates:
                    # 选择概率最高的候选球
                    candidates.sort(key=lambda x: x[1], reverse=True)
                    result_balls.remove(ball)
                    result_balls.append(candidates[0][0])
                    excess -= 1

        elif current_repeat_count < target_repeat_count:
            # 需要增加重号数
            needed = target_repeat_count - current_repeat_count
            repeat_balls_not_in_result = [ball for ball in self.latest_period['red_balls'] if ball not in result_balls]

            # 尝试添加重号球，同时保持其他约束
            for ball in repeat_balls_not_in_result:
                if needed <= 0:
                    break

                # 检查添加这个球后是否还能满足约束
                # 需要移除一个现有的球
                for existing_ball in result_balls:
                    if existing_ball in self.latest_period['red_balls']:
                        continue  # 不移除重号球

                    temp_result = [b for b in result_balls if b != existing_ball] + [ball]
                    temp_big_count = sum(1 for b in temp_result if self._is_big_ball(b, 'red'))
                    temp_cold_count = sum(1 for b in temp_result if b in red_cold_balls)

                    if temp_big_count == target_big_count and temp_cold_count == target_cold_count:
                        result_balls.remove(existing_ball)
                        result_balls.append(ball)
                        needed -= 1
                        break

        return result_balls[:6]
