# -*- coding: utf-8 -*-
"""
测试Excel导出修复效果
验证Excel导出功能是否使用了修复后的数据源
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ssq_lottery_system import SSQLotterySystem
import pandas as pd
import copy


def test_excel_export_fix():
    """测试Excel导出修复效果"""
    print("=== 测试Excel导出修复效果 ===")
    
    try:
        # 创建系统实例
        system = SSQLotterySystem("lottery_data_all.xlsx")
        
        # 模拟分析比对流程
        target_period = "25067"
        database_range = 99
        
        print(f"分析期号: {target_period}")
        print(f"数据库范围: {database_range}")
        
        # 获取当前数据库
        current_database = system.data_loader.get_database_for_period(
            target_period, database_range
        )
        
        # 获取答案数据
        answer_data = system.data_loader.get_answer_data(target_period)
        
        # 运行预测
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 分析
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = system.prediction_engine.generate_all_predictions(
            current_database, latest_period
        )
        
        # 保存第9组预测的原始数据
        original_pred_9 = copy.deepcopy(predictions[9])
        print(f"\n第9组原始预测:")
        print(f"  红球: {original_pred_9['red_balls']}")
        print(f"  蓝球: {original_pred_9['blue_ball']}")
        print(f"  方法: {original_pred_9['method']}")
        
        # 比对结果
        comparison_result = system.comparison_engine.compare_predictions(
            predictions, answer_data
        )
        
        # 模拟主程序的results存储
        results = []
        results.append({
            'period': target_period,
            'predictions': predictions,
            'comparison': comparison_result,
            'database_size': len(current_database),
            'latest_period': latest_period
        })
        
        # 检查修复前后的数据源差异
        print(f"\n=== 检查数据源差异 ===")
        
        result = results[0]
        predictions_from_result = result['predictions']
        comparison_from_result = result['comparison']
        
        # 修复前的数据源（从results['predictions']获取）
        pred_old_way = predictions_from_result[9]
        
        # 修复后的数据源（从results['comparison'][group_id]['prediction']获取）
        pred_new_way = comparison_from_result[9]['prediction']
        
        print(f"修复前数据源（results['predictions'][9]）:")
        print(f"  红球: {pred_old_way['red_balls']}")
        print(f"  蓝球: {pred_old_way['blue_ball']}")
        print(f"  方法: {pred_old_way['method']}")
        
        print(f"修复后数据源（results['comparison'][9]['prediction']）:")
        print(f"  红球: {pred_new_way['red_balls']}")
        print(f"  蓝球: {pred_new_way['blue_ball']}")
        print(f"  方法: {pred_new_way['method']}")
        
        print(f"是否相同对象: {pred_old_way is pred_new_way}")
        print(f"内容是否相等: {pred_old_way == pred_new_way}")
        
        # 模拟修改原始预测数据
        print(f"\n=== 模拟数据修改测试 ===")
        
        # 保存原始值
        original_red_0 = pred_old_way['red_balls'][0]
        original_blue = pred_old_way['blue_ball']
        original_method = pred_old_way['method']
        
        print(f"修改前:")
        print(f"  原始数据红球[0]: {pred_old_way['red_balls'][0]}")
        print(f"  原始数据蓝球: {pred_old_way['blue_ball']}")
        print(f"  原始数据方法: {pred_old_way['method']}")
        print(f"  比对数据红球[0]: {pred_new_way['red_balls'][0]}")
        print(f"  比对数据蓝球: {pred_new_way['blue_ball']}")
        print(f"  比对数据方法: {pred_new_way['method']}")
        
        # 修改原始预测数据
        pred_old_way['red_balls'][0] = 99
        pred_old_way['blue_ball'] = 99
        pred_old_way['method'] = "修改后的方法"
        
        print(f"修改后:")
        print(f"  原始数据红球[0]: {pred_old_way['red_balls'][0]}")
        print(f"  原始数据蓝球: {pred_old_way['blue_ball']}")
        print(f"  原始数据方法: {pred_old_way['method']}")
        print(f"  比对数据红球[0]: {pred_new_way['red_balls'][0]}")
        print(f"  比对数据蓝球: {pred_new_way['blue_ball']}")
        print(f"  比对数据方法: {pred_new_way['method']}")
        
        # 检查修复效果
        if (pred_old_way['red_balls'][0] != pred_new_way['red_balls'][0] or
            pred_old_way['blue_ball'] != pred_new_way['blue_ball'] or
            pred_old_way['method'] != pred_new_way['method']):
            print("✅ 数据隔离成功！修改原始数据不影响比对结果中的数据")
        else:
            print("❌ 数据隔离失败！仍然存在引用问题")
        
        # 测试Excel导出功能
        print(f"\n=== 测试Excel导出功能 ===")
        
        # 导出Excel文件
        system.export_manager.export_analysis_results(results)
        
        # 读取导出的Excel文件进行验证
        import glob
        excel_files = glob.glob("output/analysis_results_*.xlsx")
        if excel_files:
            latest_excel = max(excel_files, key=os.path.getctime)
            print(f"找到导出的Excel文件: {latest_excel}")
            
            # 读取详细比对结果工作表
            try:
                df_detailed = pd.read_excel(latest_excel, sheet_name='详细比对结果')
                
                # 查找第9组的数据
                group_9_data = df_detailed[df_detailed['预测组号'] == 9]
                
                if not group_9_data.empty:
                    row = group_9_data.iloc[0]
                    excel_red_balls = row['预测红球']
                    excel_blue_ball = row['预测蓝球']
                    excel_method = row['预测方法']
                    
                    print(f"\nExcel中第9组数据:")
                    print(f"  红球: {excel_red_balls}")
                    print(f"  蓝球: {excel_blue_ball}")
                    print(f"  方法: {excel_method}")
                    
                    # 验证Excel中的数据是否使用了修复后的数据源
                    expected_red = ' '.join(map(str, pred_new_way['red_balls']))
                    expected_blue = pred_new_way['blue_ball']
                    expected_method = pred_new_way['method']
                    
                    print(f"\n期望的数据（修复后数据源）:")
                    print(f"  红球: {expected_red}")
                    print(f"  蓝球: {expected_blue}")
                    print(f"  方法: {expected_method}")
                    
                    if (excel_red_balls == expected_red and
                        excel_blue_ball == expected_blue and
                        excel_method == expected_method):
                        print("✅ Excel导出使用了修复后的数据源")
                    else:
                        print("❌ Excel导出仍然使用了原始数据源")
                        
                        # 检查是否使用了被修改的原始数据
                        modified_red = ' '.join(map(str, pred_old_way['red_balls']))
                        modified_blue = pred_old_way['blue_ball']
                        modified_method = pred_old_way['method']
                        
                        if (excel_red_balls == modified_red and
                            excel_blue_ball == modified_blue and
                            excel_method == modified_method):
                            print("❌ Excel导出使用了被修改的原始数据！")
                        else:
                            print("? Excel导出的数据来源不明")
                else:
                    print("❌ 在Excel中没有找到第9组数据")
                    
            except Exception as e:
                print(f"读取Excel文件失败: {e}")
        else:
            print("❌ 没有找到导出的Excel文件")
        
        # 恢复原始值
        pred_old_way['red_balls'][0] = original_red_0
        pred_old_way['blue_ball'] = original_blue
        pred_old_way['method'] = original_method
        
        print(f"\n✅ Excel导出修复测试完成！")
        return True
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_excel_export_fix()
