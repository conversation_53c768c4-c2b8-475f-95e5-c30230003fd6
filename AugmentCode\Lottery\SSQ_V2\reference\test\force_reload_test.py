#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制重新加载模块测试
清除Python缓存并重新测试
"""

import sys
import importlib
import os

# 清除模块缓存
modules_to_reload = [
    'ssq_lottery_system',
    'modules.data_loader',
    'modules.statistical_analyzer',
    'modules.markov_chain',
    'modules.bayesian_analyzer',
    'modules.prediction_engine',
    'modules.comparison_engine',
    'modules.export_manager',
    'modules.user_interface'
]

print("=== 清除Python模块缓存 ===")
for module_name in modules_to_reload:
    if module_name in sys.modules:
        print(f"重新加载模块: {module_name}")
        importlib.reload(sys.modules[module_name])
    else:
        print(f"模块未加载: {module_name}")

# 清除__pycache__目录
print("\n=== 清除__pycache__目录 ===")
import shutil

def remove_pycache(directory):
    """递归删除__pycache__目录"""
    for root, dirs, files in os.walk(directory):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            print(f"删除: {pycache_path}")
            try:
                shutil.rmtree(pycache_path)
            except Exception as e:
                print(f"删除失败: {e}")

# 删除当前目录和modules目录的缓存
remove_pycache('.')
remove_pycache('modules')

print("\n=== 重新导入并测试 ===")

# 重新导入主模块
from ssq_lottery_system import SSQLotterySystem

def test_after_cache_clear():
    """清除缓存后测试"""
    print("=== 清除缓存后测试重号数计算 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 检查方法是否存在
    if hasattr(system, '_calculate_repeat_count_for_analysis'):
        print("✓ _calculate_repeat_count_for_analysis 方法存在")
    else:
        print("❌ _calculate_repeat_count_for_analysis 方法不存在")
        return False
    
    # 用户的参数
    start_period = '25001'
    database_range = 99
    
    # 获取第50期数据
    analysis_periods = system.data_loader.get_analysis_periods(start_period)
    period_50 = analysis_periods[49]  # 第50期
    
    current_database = system.data_loader.get_database_for_period(period_50, database_range)
    latest_period = system.data_loader.get_latest_period(current_database)
    
    print(f"期号: {latest_period['period']}")
    red_str = ' '.join(map(str, latest_period['red_balls']))
    print(f"号码: {latest_period['period']} {red_str} + {latest_period['blue_ball']}")
    
    # 测试重号数计算
    repeat_count = system._calculate_repeat_count_for_analysis(current_database, latest_period)
    print(f"重号数: {repeat_count}")
    
    # 手动验证
    prev_period_num = 25049
    prev_row = None
    for _, row in current_database.iterrows():
        if row['NO'] == prev_period_num:
            prev_row = row
            break
    
    if prev_row is not None:
        prev_red_balls = [int(prev_row[f'r{j}']) for j in range(1, 7)]
        current_red_balls = latest_period['red_balls']
        repeat_balls = [ball for ball in current_red_balls if ball in prev_red_balls]
        manual_repeat_count = len(repeat_balls)
        
        print(f"手动计算重号数: {manual_repeat_count}")
        print(f"重号球: {repeat_balls}")
        
        if repeat_count == manual_repeat_count == 1:
            print("✅ 重号数计算正确")
            return True
        else:
            print("❌ 重号数计算错误")
            return False
    else:
        print("❌ 未找到上一期数据")
        return False


def main():
    """主函数"""
    print("强制重新加载模块测试")
    print("=" * 60)
    
    success = test_after_cache_clear()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 清除缓存后测试通过！")
        print("重号数计算正确")
        print("\n建议用户：")
        print("1. 删除所有__pycache__目录")
        print("2. 重新启动Python解释器")
        print("3. 重新运行主程序")
    else:
        print("❌ 清除缓存后测试仍然失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
