# -*- coding: utf-8 -*-
"""
比对引擎模块 (Comparison Engine Module)

负责将预测的复式红蓝球号码与答案数据进行比对，计算命中情况。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional


class ComparisonEngine:
    """
    比对引擎类
    
    负责预测结果与答案数据的比对
    """
    
    def __init__(self, config: Dict):
        """
        初始化比对引擎
        
        Args:
            config: 系统配置字典
        """
        self.config = config
    
    def compare_predictions(self, predictions: Dict, answer_data: List[Dict]) -> Dict:
        """
        比对预测结果与答案数据
        
        Args:
            predictions: 预测结果字典
            answer_data: 答案数据列表（6期）
            
        Returns:
            比对结果字典
        """
        comparison_results = {}
        
        for group_id, prediction in predictions.items():
            comparison_results[group_id] = self._compare_single_prediction(
                prediction, answer_data
            )
        
        return comparison_results
    
    def _compare_single_prediction(self, prediction: Dict, 
                                 answer_data: List[Dict]) -> Dict:
        """
        比对单个预测结果与答案数据
        
        Args:
            prediction: 单个预测结果
            answer_data: 答案数据列表（6期）
            
        Returns:
            单个预测的比对结果
        """
        pred_red_balls = set(prediction['red_balls'])
        pred_blue_balls = set(prediction['blue_balls'])
        
        max_hit_count = 0
        max_hit_details = None
        hit_details_list = []
        
        # 与每一期答案数据比对
        for answer in answer_data:
            answer_red_balls = set(answer['red_balls'])
            answer_blue_ball = answer['blue_ball']
            
            # 计算红球命中数
            red_hits = len(pred_red_balls & answer_red_balls)
            
            # 计算蓝球命中数
            blue_hits = 1 if answer_blue_ball in pred_blue_balls else 0
            
            # 总命中数
            total_hits = red_hits + blue_hits
            
            hit_details = {
                'period': answer['period'],
                'red_hits': red_hits,
                'blue_hits': blue_hits,
                'total_hits': total_hits,
                'answer_red_balls': answer['red_balls'],
                'answer_blue_ball': answer_blue_ball,
                'hit_red_balls': list(pred_red_balls & answer_red_balls)
            }
            
            hit_details_list.append(hit_details)
            
            # 更新最大命中情况
            if total_hits > max_hit_count:
                max_hit_count = total_hits
                max_hit_details = hit_details
        
        return {
            'prediction': prediction,
            'max_hit_count': max_hit_count,
            'max_hit_details': max_hit_details,
            'all_hit_details': hit_details_list
        }
    
    def get_hit_statistics(self, comparison_results: Dict) -> Dict:
        """
        获取命中统计信息
        
        Args:
            comparison_results: 比对结果字典
            
        Returns:
            命中统计字典
        """
        hit_distribution = {}
        
        for group_id, result in comparison_results.items():
            max_hit = result['max_hit_count']
            
            if max_hit not in hit_distribution:
                hit_distribution[max_hit] = 0
            hit_distribution[max_hit] += 1
        
        return {
            'hit_distribution': hit_distribution,
            'total_groups': len(comparison_results)
        }
    
    def format_comparison_result(self, group_id: int, result: Dict) -> str:
        """
        格式化比对结果显示
        
        Args:
            group_id: 预测组号
            result: 比对结果
            
        Returns:
            格式化的显示字符串
        """
        prediction = result['prediction']
        max_hit = result['max_hit_details']
        
        red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
        blue_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['blue_balls'])
        
        result_str = f"第{group_id}组预测的号码为：{red_balls_str} + {blue_balls_str} {prediction['method']}\n"
        
        if max_hit:
            result_str += f"最大命中情况：{max_hit['total_hits']}球 "
            result_str += f"(期号：{max_hit['period']}，红球：{max_hit['red_hits']}个，蓝球：{max_hit['blue_hits']}个)"
        
        return result_str
