#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球彩票预测与分析系统演示脚本

展示系统的主要功能
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.data_loader import DataLoader
from modules.statistical_analyzer import StatisticalAnalyzer
from modules.markov_chain import MarkovChainAnalyzer
from modules.prediction_engine import PredictionEngine
from modules.comparison_engine import ComparisonEngine
from modules.export_manager import ExportManager


def demo_prediction():
    """演示预测功能"""
    print("=" * 60)
    print("演示：预测选号功能")
    print("=" * 60)
    
    # 系统配置
    config = {
        'red_ball_range': (1, 33),
        'blue_ball_range': (1, 16),
        'red_ball_count': 6,
        'blue_ball_count': 1,
        'answer_periods': 6
    }
    
    # 初始化组件
    data_loader = DataLoader("lottery_data_all.xlsx")
    statistical_analyzer = StatisticalAnalyzer(config)
    markov_analyzer = MarkovChainAnalyzer(config)
    prediction_engine = PredictionEngine(config, statistical_analyzer, markov_analyzer)
    
    # 设置参数
    target_period = "25100"  # 目标期号
    long_term_range = 500    # 远期数据范围
    short_term_range = 50    # 近期数据范围
    
    print(f"目标期号: {target_period}")
    print(f"远期数据范围: {long_term_range}期")
    print(f"近期数据范围: {short_term_range}期")
    
    # 获取数据
    long_term_database = data_loader.get_database_for_period(target_period, long_term_range)
    short_term_database = data_loader.get_database_for_period(target_period, short_term_range)
    
    if long_term_database is None or short_term_database is None:
        print("数据获取失败！")
        return
    
    print(f"远期数据库: {len(long_term_database)}期")
    print(f"近期数据库: {len(short_term_database)}期")
    
    # 获取最新一期信息
    latest_period = data_loader.get_latest_period(long_term_database)
    if latest_period:
        red_balls_str = ' '.join(f"{ball:2d}" for ball in latest_period['red_balls'])
        print(f"最新一期: {latest_period['period']} {red_balls_str} + {latest_period['blue_ball']:2d}")
    
    # 运行分析
    print("\n正在进行统计分析...")
    statistical_analyzer.analyze(long_term_database)
    long_term_stats = statistical_analyzer.get_probability_tables()
    
    statistical_analyzer.analyze(short_term_database)
    short_term_stats = statistical_analyzer.get_probability_tables()
    
    print("正在进行马尔科夫链分析...")
    markov_analyzer.analyze(long_term_database, latest_period)
    long_term_markov = markov_analyzer.get_probability_tables()
    
    markov_analyzer.analyze(short_term_database, latest_period)
    short_term_markov = markov_analyzer.get_probability_tables()
    
    # 生成预测
    print("正在生成预测...")
    predictions = prediction_engine.generate_predictions(
        long_term_stats, short_term_stats,
        long_term_markov, short_term_markov,
        latest_period
    )
    
    # 显示预测结果
    print("\n=== 预测结果 ===")
    for group_id, prediction in predictions.items():
        red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
        blue_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['blue_balls'])
        print(f"第{group_id}组预测的号码为：{red_balls_str} + {blue_balls_str} {prediction['method']}")
    
    return predictions


def demo_analysis():
    """演示分析比对功能"""
    print("\n" + "=" * 60)
    print("演示：分析比对功能")
    print("=" * 60)
    
    # 系统配置
    config = {
        'red_ball_range': (1, 33),
        'blue_ball_range': (1, 16),
        'red_ball_count': 6,
        'blue_ball_count': 1,
        'answer_periods': 6
    }
    
    # 初始化组件
    data_loader = DataLoader("lottery_data_all.xlsx")
    statistical_analyzer = StatisticalAnalyzer(config)
    markov_analyzer = MarkovChainAnalyzer(config)
    prediction_engine = PredictionEngine(config, statistical_analyzer, markov_analyzer)
    comparison_engine = ComparisonEngine(config)
    
    # 设置参数（分析最近10期）
    start_period = "25070"  # 使用更早的期号确保有足够的后续数据
    long_term_range = 500
    short_term_range = 50
    
    print(f"开始分析期号: {start_period}")
    print(f"远期数据范围: {long_term_range}期")
    print(f"近期数据范围: {short_term_range}期")
    
    # 获取可分析的期号
    analysis_periods = data_loader.get_analysis_periods(start_period)
    if not analysis_periods:
        print("没有可分析的期号！")
        return
    
    # 限制分析期数（演示用）
    analysis_periods = analysis_periods[:10]
    print(f"将分析 {len(analysis_periods)} 期数据")
    
    results = []
    
    for i, period in enumerate(analysis_periods):
        print(f"\n正在分析第 {i+1}/{len(analysis_periods)} 期: {period}")
        
        # 获取当前数据库
        long_term_database = data_loader.get_database_for_period(period, long_term_range)
        short_term_database = data_loader.get_database_for_period(period, short_term_range)
        
        if (long_term_database is None or len(long_term_database) == 0 or
            short_term_database is None or len(short_term_database) == 0):
            continue
        
        # 获取答案数据
        answer_data = data_loader.get_answer_data(period)
        if answer_data is None or len(answer_data) == 0:
            continue
        
        # 运行预测
        latest_period = data_loader.get_latest_period(long_term_database)
        
        # 统计分析
        statistical_analyzer.analyze(long_term_database)
        long_term_stats = statistical_analyzer.get_probability_tables()
        
        statistical_analyzer.analyze(short_term_database)
        short_term_stats = statistical_analyzer.get_probability_tables()
        
        # 马尔科夫链分析
        markov_analyzer.analyze(long_term_database, latest_period)
        long_term_markov = markov_analyzer.get_probability_tables()
        
        markov_analyzer.analyze(short_term_database, latest_period)
        short_term_markov = markov_analyzer.get_probability_tables()
        
        # 生成预测
        predictions = prediction_engine.generate_predictions(
            long_term_stats, short_term_stats,
            long_term_markov, short_term_markov,
            latest_period
        )
        
        # 比对结果
        comparison_result = comparison_engine.compare_predictions(predictions, answer_data)
        
        # 显示结果
        for group_id, result in comparison_result.items():
            prediction = result['prediction']
            max_hit = result['max_hit_details']
            
            red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
            blue_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['blue_balls'])
            
            hit_info = ""
            if max_hit:
                hit_info = f"最大命中: {max_hit['total_hits']}球 (期号: {max_hit['period']})"
            
            print(f"  第{group_id}组: {red_balls_str} + {blue_balls_str} {hit_info}")
        
        results.append({
            'period': period,
            'predictions': predictions,
            'comparison': comparison_result
        })
    
    # 统计最终结果
    print("\n=== 最终统计结果 ===")
    hit_distribution = {}
    
    for result in results:
        for group_id, comparison in result['comparison'].items():
            max_hit = comparison['max_hit_count']
            if max_hit not in hit_distribution:
                hit_distribution[max_hit] = 0
            hit_distribution[max_hit] += 1
    
    print("命中分布统计：")
    for hit_count in sorted(hit_distribution.keys(), reverse=True):
        count = hit_distribution[hit_count]
        print(f"{hit_count}球命中：{count}次")
    
    return results


def main():
    """主演示函数"""
    print("双色球彩票预测与分析系统 - 功能演示")
    print("=" * 60)
    
    try:
        # 检查数据文件
        if not os.path.exists("lottery_data_all.xlsx"):
            print("错误: 数据文件 'lottery_data_all.xlsx' 不存在！")
            return
        
        # 演示预测功能
        predictions = demo_prediction()
        
        # 演示分析功能
        results = demo_analysis()
        
        print("\n" + "=" * 60)
        print("演示完成！")
        print("=" * 60)
        print("系统功能正常，可以使用以下命令运行完整程序：")
        print("python ssq_lottery_system.py")
        
    except Exception as e:
        print(f"演示过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
