# -*- coding: utf-8 -*-
"""
导出管理模块 (Export Manager Module)

负责将预测结果和分析结果导出到Excel文件。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional
from datetime import datetime
import os


class ExportManager:
    """
    导出管理器类
    
    负责数据导出功能
    """
    
    def __init__(self):
        """初始化导出管理器"""
        self.output_dir = "output"
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
    
    def export_prediction_results(self, predictions: Dict,
                                probability_tables: Dict,
                                long_term_database: pd.DataFrame,
                                short_term_database: pd.DataFrame = None):
        """
        导出预测结果到Excel文件

        Args:
            predictions: 预测结果字典
            probability_tables: 概率表格字典
            long_term_database: 远期数据库DataFrame
            short_term_database: 近期数据库DataFrame
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.output_dir}/prediction_results_{timestamp}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 导出预测结果
            self._export_predictions_sheet(predictions, writer)
            
            # 导出概率表格
            self._export_probability_tables_sheet(probability_tables, writer)

            # 导出跟随性概率矩阵
            self._export_follow_probability_matrices_sheet(probability_tables, writer)

            # 导出数据库信息
            self._export_database_info_sheet(long_term_database, short_term_database, writer)
        
        print(f"预测结果已保存到: {filename}")
    
    def export_analysis_results(self, results: List[Dict]):
        """
        导出分析结果到Excel文件
        
        Args:
            results: 分析结果列表
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.output_dir}/analysis_results_{timestamp}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 导出分析结果汇总
            self._export_analysis_summary_sheet(results, writer)
            
            # 导出详细比对结果
            self._export_detailed_comparison_sheet(results, writer)
            
            # 导出命中统计
            self._export_hit_statistics_sheet(results, writer)
        
        print(f"分析结果已保存到: {filename}")
    
    def _export_predictions_sheet(self, predictions: Dict, writer):
        """导出预测结果工作表"""
        prediction_data = []
        
        for group_id, prediction in predictions.items():
            red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
            blue_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['blue_balls'])
            
            prediction_data.append({
                '预测组号': f"第{group_id}组",
                '预测方法': prediction['method'],
                '红球号码': red_balls_str,
                '蓝球号码': blue_balls_str,
                '复式号码': f"{red_balls_str} + {blue_balls_str}"
            })
        
        df_predictions = pd.DataFrame(prediction_data)
        df_predictions.to_excel(writer, sheet_name='预测结果', index=False)
    
    def _export_probability_tables_sheet(self, probability_tables: Dict, writer):
        """导出概率表格工作表"""
        # 导出远期红球历史出现概率
        if 'long_term_statistical' in probability_tables:
            long_term_stats = probability_tables['long_term_statistical']
            if 'red_ball_probabilities' in long_term_stats:
                red_probs = long_term_stats['red_ball_probabilities']
                red_prob_data = [
                    {'红球号码': ball, '远期历史出现概率': prob}
                    for ball, prob in sorted(red_probs.items())
                ]
                df_red_probs = pd.DataFrame(red_prob_data)
                df_red_probs.to_excel(writer, sheet_name='远期红球历史概率', index=False)

            # 导出远期蓝球历史出现概率
            if 'blue_ball_probabilities' in long_term_stats:
                blue_probs = long_term_stats['blue_ball_probabilities']
                blue_prob_data = [
                    {'蓝球号码': ball, '远期历史出现概率': prob}
                    for ball, prob in sorted(blue_probs.items())
                ]
                df_blue_probs = pd.DataFrame(blue_prob_data)
                df_blue_probs.to_excel(writer, sheet_name='远期蓝球历史概率', index=False)

        # 导出近期红球历史出现概率
        if 'short_term_statistical' in probability_tables:
            short_term_stats = probability_tables['short_term_statistical']
            if 'red_ball_probabilities' in short_term_stats:
                red_probs = short_term_stats['red_ball_probabilities']
                red_prob_data = [
                    {'红球号码': ball, '近期历史出现概率': prob}
                    for ball, prob in sorted(red_probs.items())
                ]
                df_red_probs = pd.DataFrame(red_prob_data)
                df_red_probs.to_excel(writer, sheet_name='近期红球历史概率', index=False)

            # 导出近期蓝球历史出现概率
            if 'blue_ball_probabilities' in short_term_stats:
                blue_probs = short_term_stats['blue_ball_probabilities']
                blue_prob_data = [
                    {'蓝球号码': ball, '近期历史出现概率': prob}
                    for ball, prob in sorted(blue_probs.items())
                ]
                df_blue_probs = pd.DataFrame(blue_prob_data)
                df_blue_probs.to_excel(writer, sheet_name='近期蓝球历史概率', index=False)

        # 导出远期马尔科夫链概率
        if 'long_term_markov' in probability_tables:
            long_term_markov = probability_tables['long_term_markov']
            if 'red_markov_probabilities' in long_term_markov:
                red_markov = long_term_markov['red_markov_probabilities']
                if red_markov is not None:
                    red_markov_data = [
                        {'红球号码': i + 1, '远期马尔科夫链概率': prob}
                        for i, prob in enumerate(red_markov)
                    ]
                    df_red_markov = pd.DataFrame(red_markov_data)
                    df_red_markov.to_excel(writer, sheet_name='远期红球马尔科夫概率', index=False)

            if 'blue_markov_probabilities' in long_term_markov:
                blue_markov = long_term_markov['blue_markov_probabilities']
                if blue_markov is not None:
                    blue_markov_data = [
                        {'蓝球号码': i + 1, '远期马尔科夫链概率': prob}
                        for i, prob in enumerate(blue_markov)
                    ]
                    df_blue_markov = pd.DataFrame(blue_markov_data)
                    df_blue_markov.to_excel(writer, sheet_name='远期蓝球马尔科夫概率', index=False)

        # 导出近期马尔科夫链概率
        if 'short_term_markov' in probability_tables:
            short_term_markov = probability_tables['short_term_markov']
            if 'red_markov_probabilities' in short_term_markov:
                red_markov = short_term_markov['red_markov_probabilities']
                if red_markov is not None:
                    red_markov_data = [
                        {'红球号码': i + 1, '近期马尔科夫链概率': prob}
                        for i, prob in enumerate(red_markov)
                    ]
                    df_red_markov = pd.DataFrame(red_markov_data)
                    df_red_markov.to_excel(writer, sheet_name='近期红球马尔科夫概率', index=False)

            if 'blue_markov_probabilities' in short_term_markov:
                blue_markov = short_term_markov['blue_markov_probabilities']
                if blue_markov is not None:
                    blue_markov_data = [
                        {'蓝球号码': i + 1, '近期马尔科夫链概率': prob}
                        for i, prob in enumerate(blue_markov)
                    ]
                    df_blue_markov = pd.DataFrame(blue_markov_data)
                    df_blue_markov.to_excel(writer, sheet_name='近期蓝球马尔科夫概率', index=False)

    def _export_follow_probability_matrices_sheet(self, probability_tables: Dict, writer):
        """导出跟随性概率矩阵工作表"""
        # 导出远期红球跟随性概率矩阵
        if 'long_term_statistical' in probability_tables:
            long_term_stats = probability_tables['long_term_statistical']
            if 'red_follow_matrix' in long_term_stats:
                red_follow_matrix = long_term_stats['red_follow_matrix']
                if red_follow_matrix is not None:
                    # 创建DataFrame，行和列都是红球号码
                    df_red_follow = pd.DataFrame(
                        red_follow_matrix,
                        index=[f'红球{i+1}' for i in range(33)],
                        columns=[f'红球{i+1}' for i in range(33)]
                    )
                    df_red_follow.to_excel(writer, sheet_name='远期红球跟随性概率矩阵')

            if 'blue_follow_matrix' in long_term_stats:
                blue_follow_matrix = long_term_stats['blue_follow_matrix']
                if blue_follow_matrix is not None:
                    # 创建DataFrame，行和列都是蓝球号码
                    df_blue_follow = pd.DataFrame(
                        blue_follow_matrix,
                        index=[f'蓝球{i+1}' for i in range(16)],
                        columns=[f'蓝球{i+1}' for i in range(16)]
                    )
                    df_blue_follow.to_excel(writer, sheet_name='远期蓝球跟随性概率矩阵')

        # 导出近期红球跟随性概率矩阵
        if 'short_term_statistical' in probability_tables:
            short_term_stats = probability_tables['short_term_statistical']
            if 'red_follow_matrix' in short_term_stats:
                red_follow_matrix = short_term_stats['red_follow_matrix']
                if red_follow_matrix is not None:
                    # 创建DataFrame，行和列都是红球号码
                    df_red_follow = pd.DataFrame(
                        red_follow_matrix,
                        index=[f'红球{i+1}' for i in range(33)],
                        columns=[f'红球{i+1}' for i in range(33)]
                    )
                    df_red_follow.to_excel(writer, sheet_name='近期红球跟随性概率矩阵')

            if 'blue_follow_matrix' in short_term_stats:
                blue_follow_matrix = short_term_stats['blue_follow_matrix']
                if blue_follow_matrix is not None:
                    # 创建DataFrame，行和列都是蓝球号码
                    df_blue_follow = pd.DataFrame(
                        blue_follow_matrix,
                        index=[f'蓝球{i+1}' for i in range(16)],
                        columns=[f'蓝球{i+1}' for i in range(16)]
                    )
                    df_blue_follow.to_excel(writer, sheet_name='近期蓝球跟随性概率矩阵')
    
    def _export_database_info_sheet(self, long_term_database: pd.DataFrame,
                                   short_term_database: pd.DataFrame, writer):
        """导出数据库信息工作表"""
        info_data = [
            {'项目': '远期数据总期数', '值': len(long_term_database)},
            {'项目': '远期数据起始期号', '值': min(long_term_database['NO'])},
            {'项目': '远期数据结束期号', '值': max(long_term_database['NO'])},
        ]

        if short_term_database is not None:
            info_data.extend([
                {'项目': '近期数据总期数', '值': len(short_term_database)},
                {'项目': '近期数据起始期号', '值': min(short_term_database['NO'])},
                {'项目': '近期数据结束期号', '值': max(short_term_database['NO'])},
            ])

        info_data.append({'项目': '导出时间', '值': datetime.now().strftime("%Y-%m-%d %H:%M:%S")})

        df_info = pd.DataFrame(info_data)
        df_info.to_excel(writer, sheet_name='数据库信息', index=False)
    
    def _export_analysis_summary_sheet(self, results: List[Dict], writer):
        """导出分析结果汇总工作表"""
        summary_data = []
        
        for result in results:
            period = result['period']
            latest_period = result['latest_period']
            
            for group_id, comparison in result['comparison'].items():
                prediction = comparison['prediction']
                max_hit = comparison['max_hit_details']
                
                red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
                blue_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['blue_balls'])
                
                summary_data.append({
                    '分析期号': period,
                    '预测组号': f"第{group_id}组",
                    '预测方法': prediction['method'],
                    '预测红球': red_balls_str,
                    '预测蓝球': blue_balls_str,
                    '最大命中数': max_hit['total_hits'] if max_hit else 0,
                    '最大命中期号': max_hit['period'] if max_hit else '',
                    '红球命中数': max_hit['red_hits'] if max_hit else 0,
                    '蓝球命中数': max_hit['blue_hits'] if max_hit else 0,
                    '蓝球命中状态': '是' if max_hit and max_hit['blue_hits'] == 1 else '否'
                })
        
        df_summary = pd.DataFrame(summary_data)
        df_summary.to_excel(writer, sheet_name='分析结果汇总', index=False)
    
    def _export_detailed_comparison_sheet(self, results: List[Dict], writer):
        """导出详细比对结果工作表"""
        detailed_data = []
        
        for result in results:
            period = result['period']
            
            for group_id, comparison in result['comparison'].items():
                prediction = comparison['prediction']
                
                for hit_detail in comparison['all_hit_details']:
                    red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
                    blue_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['blue_balls'])
                    answer_red_str = ' '.join(f"{ball:2d}" for ball in hit_detail['answer_red_balls'])
                    hit_red_str = ' '.join(f"{ball:2d}" for ball in hit_detail['hit_red_balls'])
                    
                    detailed_data.append({
                        '分析期号': period,
                        '预测组号': f"第{group_id}组",
                        '预测方法': prediction['method'],
                        '预测红球': red_balls_str,
                        '预测蓝球': blue_balls_str,
                        '答案期号': hit_detail['period'],
                        '答案红球': answer_red_str,
                        '答案蓝球': hit_detail['answer_blue_ball'],
                        '命中红球': hit_red_str,
                        '红球命中数': hit_detail['red_hits'],
                        '蓝球命中数': hit_detail['blue_hits'],
                        '总命中数': hit_detail['total_hits']
                    })
        
        df_detailed = pd.DataFrame(detailed_data)
        df_detailed.to_excel(writer, sheet_name='详细比对结果', index=False)
    
    def _export_hit_statistics_sheet(self, results: List[Dict], writer):
        """导出命中统计工作表"""
        # 统计每组预测的命中分布
        hit_distribution = {}
        
        for result in results:
            for group_id, comparison in result['comparison'].items():
                max_hit = comparison['max_hit_count']
                
                if max_hit not in hit_distribution:
                    hit_distribution[max_hit] = 0
                hit_distribution[max_hit] += 1
        
        # 创建统计数据
        stats_data = [
            {'命中球数': hit_count, '出现次数': count}
            for hit_count, count in sorted(hit_distribution.items(), reverse=True)
        ]
        
        df_stats = pd.DataFrame(stats_data)
        df_stats.to_excel(writer, sheet_name='命中统计', index=False)
