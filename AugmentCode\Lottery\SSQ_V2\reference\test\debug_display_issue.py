# -*- coding: utf-8 -*-
"""
调试显示问题
检查预测结果显示与实际数据是否一致
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ssq_lottery_system import SSQLotterySystem


def debug_display_issue():
    """调试显示问题"""
    print("=== 调试预测结果显示问题 ===")
    
    try:
        # 创建系统实例
        system = SSQLotterySystem("lottery_data_all.xlsx")
        
        # 加载数据
        all_data = system.data_loader.get_current_database()
        
        # 找到期号25067的数据
        target_period = "25067"
        target_index = None
        
        for i, row in all_data.iterrows():
            if str(row['NO']) == target_period:
                target_index = i
                break
        
        if target_index is None:
            print(f"❌ 没有找到期号 {target_period}")
            return False
        
        print(f"✅ 找到期号 {target_period}，索引: {target_index}")
        
        # 获取数据库范围（前99期）
        database_range = 99
        start_index = max(0, target_index - database_range + 1)
        end_index = target_index + 1
        current_database = all_data.iloc[start_index:end_index].copy()
        
        # 获取最新一期数据
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 运行分析器
        print(f"\n=== 运行分析器 ===")
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        print(f"\n=== 生成预测 ===")
        predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
        
        # 检查第9组预测的详细信息
        if 9 not in predictions:
            print(f"❌ 没有找到第9组预测")
            return False
        
        prediction_9 = predictions[9]
        print(f"第9组预测详细信息:")
        print(f"  原始数据类型: {type(prediction_9)}")
        print(f"  原始数据内容: {prediction_9}")
        print(f"  红球数据类型: {[type(x) for x in prediction_9['red_balls']]}")
        print(f"  红球原始值: {prediction_9['red_balls']}")
        print(f"  蓝球数据类型: {type(prediction_9['blue_ball'])}")
        print(f"  蓝球原始值: {prediction_9['blue_ball']}")
        
        # 检查打印功能中的格式化过程
        print(f"\n=== 检查格式化过程 ===")
        
        # 模拟 _print_high_hit_details 中的格式化
        red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction_9['red_balls'])
        print(f"格式化红球字符串: '{red_balls_str}'")
        print(f"格式化蓝球: {prediction_9['blue_ball']:2d}")
        
        # 检查是否存在数据类型转换问题
        print(f"\n=== 数据类型转换测试 ===")
        try:
            converted_red = [int(x) for x in prediction_9['red_balls']]
            converted_blue = int(prediction_9['blue_ball'])
            print(f"转换后红球: {converted_red}")
            print(f"转换后蓝球: {converted_blue}")
        except Exception as e:
            print(f"转换失败: {e}")
        
        # 检查是否存在内存引用问题
        print(f"\n=== 内存引用检查 ===")
        print(f"prediction_9 id: {id(prediction_9)}")
        print(f"red_balls id: {id(prediction_9['red_balls'])}")
        print(f"blue_ball id: {id(prediction_9['blue_ball'])}")
        
        # 检查预测引擎内部状态
        print(f"\n=== 预测引擎内部状态 ===")
        if hasattr(system.prediction_engine, 'predictions'):
            internal_pred_9 = system.prediction_engine.predictions.get(9)
            if internal_pred_9:
                print(f"引擎内部第9组: {internal_pred_9}")
                print(f"是否相同对象: {internal_pred_9 is prediction_9}")
                print(f"内容是否相等: {internal_pred_9 == prediction_9}")
        
        # 多次获取预测，检查是否一致
        print(f"\n=== 多次获取预测一致性检查 ===")
        for i in range(3):
            new_predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
            new_pred_9 = new_predictions[9]
            print(f"第{i+1}次获取:")
            print(f"  红球: {new_pred_9['red_balls']}")
            print(f"  蓝球: {new_pred_9['blue_ball']}")
            print(f"  与第一次相同: {new_pred_9['red_balls'] == prediction_9['red_balls'] and new_pred_9['blue_ball'] == prediction_9['blue_ball']}")
        
        # 检查是否存在随机性问题
        print(f"\n=== 随机性检查 ===")
        # 重新初始化系统
        system2 = SSQLotterySystem("lottery_data_all.xlsx")
        system2.statistical_analyzer.analyze(current_database)
        system2.markov_analyzer.analyze(current_database, latest_period)
        system2.bayesian_analyzer.analyze(current_database, latest_period)
        predictions2 = system2.prediction_engine.generate_all_predictions(current_database, latest_period)
        pred2_9 = predictions2[9]
        
        print(f"新系统第9组预测:")
        print(f"  红球: {pred2_9['red_balls']}")
        print(f"  蓝球: {pred2_9['blue_ball']}")
        print(f"  与原系统相同: {pred2_9['red_balls'] == prediction_9['red_balls'] and pred2_9['blue_ball'] == prediction_9['blue_ball']}")
        
        return True
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    debug_display_issue()
