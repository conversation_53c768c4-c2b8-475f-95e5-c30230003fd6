#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找用户截图中的预测号码
检查是否存在预测号码 [2, 7, 10, 22, 27, 33] + 14
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def find_user_prediction():
    """查找用户截图中的预测号码"""
    print("=== 查找用户截图中的预测号码 ===")
    
    # 用户截图中的预测号码
    target_red = [2, 7, 10, 22, 27, 33]
    target_blue = 14
    
    print(f"目标预测号码: {target_red} + {target_blue}")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    try:
        # 分析25067期
        period = '25067'
        database_range = 99
        
        print(f"分析期号: {period}")
        print(f"数据库范围: {database_range}期")
        
        # 获取数据库和答案
        current_database = system.data_loader.get_database_for_period(period, database_range)
        answer_data = system.data_loader.get_answer_data(period)
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 分析
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
        
        print(f"\n当前系统生成的所有预测:")
        matching_groups = []
        
        for group_id in range(1, 25):
            if group_id in predictions:
                prediction = predictions[group_id]
                pred_red = [int(x) for x in prediction['red_balls']]
                pred_blue = int(prediction['blue_ball'])
                
                red_str = ' '.join(f"{ball:2d}" for ball in pred_red)
                print(f"  第{group_id:2d}组: {red_str} + {pred_blue:2d} ({prediction['method']})")
                
                # 检查是否匹配目标预测
                if pred_red == target_red and pred_blue == target_blue:
                    matching_groups.append(group_id)
        
        if matching_groups:
            print(f"\n✅ 找到匹配的预测组: {matching_groups}")
            
            # 分析匹配组的比对结果
            comparison_result = system.comparison_engine.compare_predictions(predictions, answer_data)
            
            for group_id in matching_groups:
                if group_id in comparison_result:
                    group_result = comparison_result[group_id]
                    max_hit = group_result['max_hit']
                    
                    print(f"\n第{group_id}组比对结果:")
                    print(f"  最大命中期号: {max_hit['period']}")
                    print(f"  最大命中球数: {max_hit['total_hits']}")
                    print(f"  红球命中数: {max_hit['red_hits']}")
                    print(f"  蓝球命中数: {max_hit['blue_hits']}")
                    print(f"  蓝球命中状态: {'是' if max_hit['blue_hits'] == 1 else '否'}")
                    print(f"  答案红球: {max_hit['answer_red_balls']}")
                    print(f"  答案蓝球: {max_hit['answer_blue_ball']}")
                    
                    # 手动验证
                    pred_red_set = set(target_red)
                    answer_red_set = set(max_hit['answer_red_balls'])
                    red_intersection = pred_red_set & answer_red_set
                    manual_red_hits = len(red_intersection)
                    manual_blue_hits = 1 if target_blue == max_hit['answer_blue_ball'] else 0
                    manual_total_hits = manual_red_hits + manual_blue_hits
                    
                    print(f"\n手动验证:")
                    print(f"  红球交集: {sorted(list(red_intersection))}")
                    print(f"  手动红球命中: {manual_red_hits}")
                    print(f"  手动蓝球命中: {manual_blue_hits}")
                    print(f"  手动总命中: {manual_total_hits}")
                    
                    if manual_total_hits == 5:
                        print(f"  ✅ 正确结果应该是5球命中")
                    else:
                        print(f"  ⚠️ 手动计算结果是{manual_total_hits}球命中")
        else:
            print(f"\n❌ 当前系统中没有找到预测号码 {target_red} + {target_blue}")
            print(f"这说明用户截图中的预测号码不是当前系统生成的")
            
            # 检查是否有相似的预测
            print(f"\n查找相似的预测:")
            for group_id in range(1, 25):
                if group_id in predictions:
                    prediction = predictions[group_id]
                    pred_red = [int(x) for x in prediction['red_balls']]
                    pred_blue = int(prediction['blue_ball'])
                    
                    # 计算红球相似度
                    pred_red_set = set(pred_red)
                    target_red_set = set(target_red)
                    common_red = len(pred_red_set & target_red_set)
                    
                    if common_red >= 4:  # 至少4个红球相同
                        red_str = ' '.join(f"{ball:2d}" for ball in pred_red)
                        print(f"  第{group_id:2d}组: {red_str} + {pred_blue:2d} (红球相同{common_red}个)")
        
        # 检查第9组的具体情况
        if 9 in predictions:
            prediction = predictions[9]
            pred_red = [int(x) for x in prediction['red_balls']]
            pred_blue = int(prediction['blue_ball'])
            
            print(f"\n第9组详细信息:")
            print(f"  当前系统预测: {pred_red} + {pred_blue}")
            print(f"  用户截图预测: {target_red} + {target_blue}")
            print(f"  预测方法: {prediction['method']}")
            
            if pred_red == target_red and pred_blue == target_blue:
                print(f"  ✅ 预测号码匹配")
            else:
                print(f"  ❌ 预测号码不匹配")
                print(f"  这解释了为什么用户看到的结果与当前系统不一致")
        
        return True
        
    except Exception as e:
        print(f"查找过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("查找用户截图中的预测号码")
    print("=" * 60)
    
    success = find_user_prediction()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 查找完成")
        print("\n结论:")
        print("1. 如果找到匹配的预测号码，说明当前系统是正确的")
        print("2. 如果没有找到，说明用户截图来自不同版本的系统")
        print("3. 用户需要确认使用的是最新生成的Excel文件")
    else:
        print("❌ 查找失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
