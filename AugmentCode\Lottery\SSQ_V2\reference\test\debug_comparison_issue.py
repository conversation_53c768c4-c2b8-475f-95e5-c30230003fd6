#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试比对问题
重新检查之前失败的测试
"""

import sys
from modules.comparison_engine import ComparisonEngine
from modules.data_loader import DataLoader


def debug_first_test_case():
    """调试第一个测试案例"""
    print("=== 调试第一个测试案例 ===")
    
    # 初始化数据加载器
    data_loader = DataLoader('lottery_data_all.xlsx')
    
    # 测试期号
    test_period = '25001'
    
    # 获取答案数据
    answer_data = data_loader.get_answer_data(test_period)
    
    print(f"获取到 {len(answer_data)} 期答案数据:")
    for i, answer in enumerate(answer_data):
        red_str = ' '.join(map(str, answer['red_balls']))
        print(f"  第{i+1}期: {answer['period']} {red_str} + {answer['blue_ball']}")
    
    # 创建测试预测
    test_prediction = {
        'red_balls': [1, 2, 3, 4, 5, 6],
        'blue_ball': 7,
        'method': '测试预测'
    }
    
    print(f"\n测试预测: {test_prediction['red_balls']} + {test_prediction['blue_ball']}")
    
    # 使用比对引擎
    config = {}
    engine = ComparisonEngine(config)
    
    # 比对单个预测
    results = engine._compare_single_prediction(test_prediction, answer_data)
    
    print(f"\n单个预测比对结果:")
    for result in results:
        print(f"  期号{result['period']}: 红{result['red_hits']}+蓝{result['blue_hits']} = {result['total_hits']}球")
    
    # 手动找最大命中
    max_hits_manual = max(result['total_hits'] for result in results)
    max_candidates = [r for r in results if r['total_hits'] == max_hits_manual]
    max_period_manual = max(max_candidates, key=lambda x: int(x['period']))['period']
    
    print(f"\n手动选择最大命中:")
    print(f"  最大命中数: {max_hits_manual}")
    print(f"  候选期号: {[c['period'] for c in max_candidates]}")
    print(f"  选择期号: {max_period_manual}")
    
    # 使用引擎的选择逻辑
    max_hit_engine = max(results, key=lambda x: (x['total_hits'], int(x['period'])))
    
    print(f"\n引擎选择最大命中:")
    print(f"  选择期号: {max_hit_engine['period']}")
    print(f"  命中数: {max_hit_engine['total_hits']}")
    
    if max_hit_engine['period'] == max_period_manual:
        print(f"  ✅ 引擎选择与手动选择一致")
    else:
        print(f"  ❌ 引擎选择与手动选择不一致")
        return False
    
    # 测试完整的比对流程
    predictions = {'group_1': test_prediction}
    comparison_results = engine.compare_predictions(predictions, answer_data)
    
    print(f"\n完整比对流程:")
    if 'group_1' in comparison_results:
        group_result = comparison_results['group_1']
        max_hit = group_result['max_hit']
        
        print(f"  最大命中期号: {max_hit['period']}")
        print(f"  最大命中数: {max_hit['total_hits']}")
        
        if max_hit['period'] == max_period_manual:
            print(f"  ✅ 完整流程选择正确")
            return True
        else:
            print(f"  ❌ 完整流程选择错误")
            print(f"  期望: {max_period_manual}, 实际: {max_hit['period']}")
            return False
    else:
        print(f"  ❌ 完整流程没有返回结果")
        return False


def debug_comparison_engine_internals():
    """调试比对引擎内部逻辑"""
    print(f"\n=== 调试比对引擎内部逻辑 ===")
    
    # 模拟简单的测试数据
    test_prediction = {
        'red_balls': [1, 2, 3, 4, 5, 6],
        'blue_ball': 7,
        'method': '测试预测'
    }
    
    answer_data = [
        {'period': '25001', 'red_balls': [1, 8, 9, 10, 11, 12], 'blue_ball': 13},  # 1红0蓝=1球
        {'period': '25002', 'red_balls': [2, 8, 9, 10, 11, 12], 'blue_ball': 13},  # 1红0蓝=1球
        {'period': '25003', 'red_balls': [7, 8, 9, 10, 11, 12], 'blue_ball': 13},  # 0红0蓝=0球
    ]
    
    print("测试数据:")
    print(f"  预测: {test_prediction['red_balls']} + {test_prediction['blue_ball']}")
    for answer in answer_data:
        red_str = ' '.join(map(str, answer['red_balls']))
        print(f"  {answer['period']}: {red_str} + {answer['blue_ball']}")
    
    config = {}
    engine = ComparisonEngine(config)
    
    # 单个预测比对
    results = engine._compare_single_prediction(test_prediction, answer_data)
    
    print(f"\n比对结果:")
    for result in results:
        print(f"  期号{result['period']}: 红{result['red_hits']}+蓝{result['blue_hits']} = {result['total_hits']}球")
    
    # 选择最大命中
    max_hit = max(results, key=lambda x: (x['total_hits'], int(x['period'])))
    
    print(f"\n最大命中选择:")
    print(f"  期号: {max_hit['period']}")
    print(f"  命中数: {max_hit['total_hits']}")
    
    # 期望选择25002（因为25001和25002都是1球，选择期号更大的）
    if max_hit['period'] == '25002' and max_hit['total_hits'] == 1:
        print(f"  ✅ 选择正确")
        return True
    else:
        print(f"  ❌ 选择错误，期望25002期1球")
        return False


def verify_comparison_logic_is_correct():
    """验证比对逻辑是否正确"""
    print(f"\n=== 验证比对逻辑是否正确 ===")
    
    # 用户的真实案例
    print("用户真实案例验证:")
    
    test_prediction = {
        'red_balls': [2, 7, 10, 22, 27, 33],
        'blue_ball': 14,
        'method': '用户案例'
    }
    
    answer_data = [
        {'period': '25068', 'red_balls': [5, 7, 8, 19, 20, 31], 'blue_ball': 7},
        {'period': '25069', 'red_balls': [2, 4, 19, 23, 27, 30], 'blue_ball': 5},
        {'period': '25070', 'red_balls': [2, 3, 15, 21, 22, 33], 'blue_ball': 6},
        {'period': '25071', 'red_balls': [1, 12, 18, 23, 25, 28], 'blue_ball': 7},
        {'period': '25072', 'red_balls': [2, 14, 17, 25, 27, 29], 'blue_ball': 5},
        {'period': '25073', 'red_balls': [2, 7, 10, 27, 30, 33], 'blue_ball': 11}
    ]
    
    config = {}
    engine = ComparisonEngine(config)
    
    # 执行比对
    predictions = {'group_1': test_prediction}
    comparison_results = engine.compare_predictions(predictions, answer_data)
    
    if 'group_1' in comparison_results:
        group_result = comparison_results['group_1']
        max_hit = group_result['max_hit']
        
        print(f"  预测: {test_prediction['red_balls']} + {test_prediction['blue_ball']}")
        print(f"  最大命中: {max_hit['total_hits']}球")
        print(f"  最大命中期号: {max_hit['period']}")
        print(f"  红球命中数: {max_hit['red_hits']}")
        print(f"  蓝球命中数: {max_hit['blue_hits']}")
        
        # 验证25073期的计算
        if max_hit['period'] == '25073':
            pred_red_set = set(test_prediction['red_balls'])
            answer_red_set = set(max_hit['answer_red_balls'])
            red_hits_manual = len(pred_red_set & answer_red_set)
            blue_hits_manual = 1 if test_prediction['blue_ball'] == max_hit['answer_blue_ball'] else 0
            total_hits_manual = red_hits_manual + blue_hits_manual
            
            print(f"\n手动验证25073期:")
            print(f"  实际号码: {max_hit['answer_red_balls']} + {max_hit['answer_blue_ball']}")
            print(f"  红球交集: {pred_red_set & answer_red_set}")
            print(f"  手动计算: 红{red_hits_manual}+蓝{blue_hits_manual} = {total_hits_manual}球")
            print(f"  引擎计算: 红{max_hit['red_hits']}+蓝{max_hit['blue_hits']} = {max_hit['total_hits']}球")
            
            if (max_hit['red_hits'] == red_hits_manual and 
                max_hit['blue_hits'] == blue_hits_manual and 
                max_hit['total_hits'] == total_hits_manual == 5):
                print(f"  ✅ 用户案例比对正确")
                return True
            else:
                print(f"  ❌ 用户案例比对错误")
                return False
        else:
            print(f"  ❌ 最大命中期号不是25073")
            return False
    else:
        print(f"  ❌ 没有比对结果")
        return False


def main():
    """主函数"""
    print("调试比对问题")
    print("=" * 60)
    
    success1 = debug_first_test_case()
    success2 = debug_comparison_engine_internals()
    success3 = verify_comparison_logic_is_correct()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3:
        print("🎉 比对逻辑调试通过！")
        print("比对引擎工作正常，与6期答案数据正确比对")
    else:
        print("❌ 比对逻辑有问题，需要修正")
    
    return success1 and success2 and success3


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
