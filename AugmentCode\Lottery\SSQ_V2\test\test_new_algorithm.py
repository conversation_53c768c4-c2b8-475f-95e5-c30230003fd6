#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新预测算法的脚本

详细测试第三次修改后的预测算法
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.data_loader import DataLoader
from modules.statistical_analyzer import StatisticalAnalyzer
from modules.markov_chain import MarkovChainAnalyzer
from modules.prediction_engine import PredictionEngine


def test_new_prediction_algorithm():
    """测试新的预测算法"""
    print("=== 测试新预测算法 ===")
    
    try:
        # 系统配置
        config = {
            'red_ball_range': (1, 33),
            'blue_ball_range': (1, 16),
            'red_ball_count': 6,
            'blue_ball_count': 1
        }
        
        # 初始化组件
        data_loader = DataLoader("lottery_data_all.xlsx")
        statistical_analyzer = StatisticalAnalyzer(config)
        markov_analyzer = MarkovChainAnalyzer(config)
        prediction_engine = PredictionEngine(config, statistical_analyzer, markov_analyzer)
        
        # 设置参数
        target_period = "25081"  # 使用最新期号
        long_term_range = 500    # 远期数据范围
        short_term_range = 50    # 近期数据范围
        
        print(f"目标期号: {target_period}")
        print(f"远期数据范围: {long_term_range}期")
        print(f"近期数据范围: {short_term_range}期")
        
        # 获取数据
        long_term_database = data_loader.get_database_for_period(target_period, long_term_range)
        short_term_database = data_loader.get_database_for_period(target_period, short_term_range)
        
        if long_term_database is None or short_term_database is None:
            print("数据获取失败！")
            return False
        
        print(f"远期数据库: {len(long_term_database)}期")
        print(f"近期数据库: {len(short_term_database)}期")
        
        # 获取最新一期信息
        latest_period = data_loader.get_latest_period(long_term_database)
        if latest_period:
            red_balls_str = ' '.join(f"{ball:2d}" for ball in latest_period['red_balls'])
            print(f"最新一期: {latest_period['period']} {red_balls_str} + {latest_period['blue_ball']:2d}")
            print(f"最新一期红球: {latest_period['red_balls']}")
        
        # 运行分析
        print("\n正在进行统计分析...")
        statistical_analyzer.analyze(long_term_database)
        long_term_stats = statistical_analyzer.get_probability_tables()
        
        statistical_analyzer.analyze(short_term_database)
        short_term_stats = statistical_analyzer.get_probability_tables()
        
        print("正在进行马尔科夫链分析...")
        markov_analyzer.analyze(long_term_database, latest_period)
        long_term_markov = markov_analyzer.get_probability_tables()
        
        markov_analyzer.analyze(short_term_database, latest_period)
        short_term_markov = markov_analyzer.get_probability_tables()
        
        # 生成预测
        print("正在生成预测...")
        predictions = prediction_engine.generate_predictions(
            long_term_stats, short_term_stats,
            long_term_markov, short_term_markov,
            latest_period
        )
        
        # 显示详细预测结果
        print("\n=== 详细预测结果 ===")
        for group_id, prediction in predictions.items():
            print(f"\n第{group_id}组预测 - {prediction['method']}:")
            
            red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
            blue_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['blue_balls'])
            print(f"预测号码: {red_balls_str} + {blue_balls_str}")
            print(f"红球数量: {len(prediction['red_balls'])}个")
            print(f"蓝球数量: {len(prediction['blue_balls'])}个")
            
            # 显示详细选择过程
            details = prediction['details']
            print("选择详情:")
            print(f"  远期红球: {details['long_term_red']}")
            print(f"  远期蓝球: {details['long_term_blue']}")
            
            if 'short_term_red_from_latest' in details:
                print(f"  从最新期红球选择: {details['short_term_red_from_latest']}")
                print(f"  从全部红球补充选择: {details['short_term_red_additional']}")
            else:
                print(f"  近期红球: {details.get('short_term_red', [])}")
                print(f"  近期蓝球: {details.get('short_term_blue', [])}")
        
        # 验证算法逻辑
        print("\n=== 算法逻辑验证 ===")
        for group_id, prediction in predictions.items():
            details = prediction['details']
            
            print(f"\n第{group_id}组验证:")
            print(f"远期红球数量: {len(details['long_term_red'])}")
            print(f"远期蓝球数量: {len(details['long_term_blue'])}")
            
            if 'short_term_red_from_latest' in details:
                print(f"从最新期选择红球数量: {len(details['short_term_red_from_latest'])}")
                print(f"补充选择红球数量: {len(details['short_term_red_additional'])}")
                
                # 验证不重复
                all_selected_red = (details['long_term_red'] + 
                                  details['short_term_red_from_latest'] + 
                                  details['short_term_red_additional'])
                print(f"总红球数量: {len(all_selected_red)}")
                print(f"去重后数量: {len(set(all_selected_red))}")
                print(f"是否有重复: {'否' if len(all_selected_red) == len(set(all_selected_red)) else '是'}")
                
                # 验证从最新期选择的红球确实来自最新期
                if details['short_term_red_from_latest']:
                    selected_from_latest = details['short_term_red_from_latest'][0]
                    is_from_latest = selected_from_latest in latest_period['red_balls']
                    print(f"从最新期选择的红球 {selected_from_latest} 是否来自最新期: {'是' if is_from_latest else '否'}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("双色球彩票预测与分析系统 - 新预测算法测试")
    print("=" * 60)
    
    # 检查数据文件
    if not os.path.exists("lottery_data_all.xlsx"):
        print("错误: 数据文件 'lottery_data_all.xlsx' 不存在！")
        return
    
    # 运行测试
    result = test_new_prediction_algorithm()
    
    print("\n" + "=" * 60)
    if result:
        print("新预测算法测试通过！")
        print("✅ 算法逻辑正确")
        print("✅ 号码数量符合预期")
        print("✅ 不重复约束生效")
        print("✅ 最新期约束生效")
    else:
        print("新预测算法测试失败！")


if __name__ == "__main__":
    main()
