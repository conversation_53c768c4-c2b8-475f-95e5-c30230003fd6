# 双色球彩票预测与分析系统 V2.0 使用指南

## 快速开始

### 1. 环境准备
确保已安装Python 3.7+和所需依赖包：
```bash
pip install pandas numpy openpyxl
```

### 2. 数据文件
确保根目录下有 `lottery_data_all.xlsx` 文件，包含历史开奖数据。

### 3. 运行程序
```bash
# 功能测试
python test_system.py

# 功能演示
python demo.py

# 完整程序
python ssq_lottery_system.py
```

## 主要功能

### 预测选号模式
1. **输入目标期号**：如 `25100`（表示2025年第100期）
2. **设置远期数据范围**：
   - 输入 `0` 使用全部历史数据
   - 输入正整数如 `500` 使用最近500期数据
3. **设置近期数据范围**：如 `50`（最近50期）
4. **查看预测结果**：
   - 第1组：历史出现概率复式
   - 第2组：马尔科夫链复式
5. **保存结果**：选择是否导出到Excel文件

### 分析比对模式
1. **输入开始期号**：如 `25070`
2. **设置数据范围**：同预测模式
3. **自动分析**：系统将逐期分析并比对
4. **查看统计**：命中分布统计结果
5. **自动保存**：结果自动保存到Excel文件

## 核心算法说明

### 历史出现概率
- 统计每个号码在历史数据中的出现频率
- 转换为概率分布（所有概率之和为1）

### 跟随性概率矩阵
- **红球**：33×33矩阵，记录相邻两期红球号码的跟随关系
- **蓝球**：16×16矩阵，记录相邻两期蓝球号码的跟随关系

### 马尔科夫链算法
基于最新一期号码和跟随性概率矩阵计算下期号码的迁移概率：
- **红球**：矩阵乘法计算33×1概率向量
- **蓝球**：直接使用跟随性概率向量

### 预测算法
- **第1组**：历史出现概率复式
  - 远期数据库：选择历史出现概率最大的6个红球+1个蓝球
  - 近期数据库：选择与远期不重复的历史出现概率最大的1个红球+1个蓝球
- **第2组**：马尔科夫链复式
  - 远期数据库：选择马尔科夫链概率最大的6个红球+1个蓝球
  - 近期数据库：选择与远期不重复的马尔科夫链概率最大的1个红球+1个蓝球

## 输出文件说明

### 预测结果文件
- **预测结果**：各组预测号码和方法
- **红球历史概率**：红球号码的历史出现概率
- **蓝球历史概率**：蓝球号码的历史出现概率
- **红球马尔科夫概率**：基于马尔科夫链的红球概率
- **蓝球马尔科夫概率**：基于马尔科夫链的蓝球概率
- **数据库信息**：使用的数据范围信息

### 分析结果文件
- **分析结果汇总**：每期预测和命中情况汇总
- **详细比对结果**：预测号码与实际开奖的详细比对
- **命中统计**：各种命中情况的分布统计

## 使用技巧

### 参数设置建议
- **远期数据范围**：建议使用500-1000期，平衡历史信息和时效性
- **近期数据范围**：建议使用20-50期，捕捉近期趋势

### 结果解读
- **命中率**：系统提供统计参考，不保证中奖
- **复式投注**：预测结果为复式号码，可根据需要选择投注方式
- **趋势分析**：关注不同算法的命中分布差异

### 注意事项
1. **数据质量**：确保历史数据准确完整
2. **期号格式**：严格按照5位数字格式输入
3. **内存使用**：大数据量分析时注意内存占用
4. **理性对待**：预测结果仅供参考，请理性投注

## 常见问题

### Q: 数据文件格式要求？
A: Excel文件，工作表名为"SSQ_data_all"，A列为期号，I-N列为红球，O列为蓝球。

### Q: 如何提高预测准确性？
A: 可以尝试不同的数据范围参数，观察历史命中分布，选择合适的策略。

### Q: 系统运行缓慢怎么办？
A: 减少分析期数或数据范围，或者在性能更好的机器上运行。

### Q: 预测结果如何使用？
A: 预测结果为复式号码，可以根据个人需要选择部分号码进行投注。

## 技术支持

如遇到技术问题，请检查：
1. Python版本和依赖包是否正确安装
2. 数据文件是否存在且格式正确
3. 输入参数是否符合要求
4. 系统内存是否充足

## 免责声明

本系统仅用于技术研究和学习目的，预测结果不构成任何投注建议。彩票具有随机性，请理性对待，量力而行。
