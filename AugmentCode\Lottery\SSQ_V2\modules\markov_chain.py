# -*- coding: utf-8 -*-
"""
马尔科夫链分析模块 (Markov Chain Analyzer Module)

负责实现马尔科夫链算法，计算基于最新一期号码的迁移概率。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional


class MarkovChainAnalyzer:
    """
    马尔科夫链分析器类
    
    负责马尔科夫链算法的实现
    """
    
    def __init__(self, config: Dict):
        """
        初始化马尔科夫链分析器
        
        Args:
            config: 系统配置字典
        """
        self.config = config
        self.database = None
        self.latest_period = None
        
        # 马尔科夫链结果存储
        self.red_markov_probabilities = None
        self.blue_markov_probabilities = None
        
        # 跟随性概率矩阵（从统计分析器获取）
        self.red_follow_matrix = None
        self.blue_follow_matrix = None
        
        # 历史出现概率（从统计分析器获取）
        self.red_ball_probabilities = None
        self.blue_ball_probabilities = None
    
    def analyze(self, database: pd.DataFrame, latest_period: Dict):
        """
        执行马尔科夫链分析
        
        Args:
            database: 数据库DataFrame
            latest_period: 最新一期数据
        """
        self.database = database
        self.latest_period = latest_period
        
        # 首先计算跟随性概率矩阵和历史出现概率
        self._calculate_follow_probabilities()
        self._calculate_historical_probabilities()
        
        # 计算马尔科夫链概率
        self._calculate_markov_probabilities()
    
    def _calculate_follow_probabilities(self):
        """计算跟随性概率矩阵"""
        # 红球跟随性概率矩阵（33x33）
        self.red_follow_matrix = np.zeros((33, 33))
        
        # 蓝球跟随性概率矩阵（16x16）
        self.blue_follow_matrix = np.zeros((16, 16))
        
        # 计算相邻两期的跟随关系
        for i in range(len(self.database) - 1):
            current_row = self.database.iloc[i]
            next_row = self.database.iloc[i + 1]
            
            # 当前期红球
            current_red = [int(current_row[f'r{j}']) for j in range(1, 7)]
            # 下一期红球
            next_red = [int(next_row[f'r{j}']) for j in range(1, 7)]
            
            # 统计红球跟随关系
            for curr_ball in current_red:
                for next_ball in next_red:
                    self.red_follow_matrix[curr_ball - 1][next_ball - 1] += 1
            
            # 蓝球跟随关系
            current_blue = int(current_row['b'])
            next_blue = int(next_row['b'])
            self.blue_follow_matrix[current_blue - 1][next_blue - 1] += 1
        
        # 转换为概率
        # 红球跟随概率矩阵：每列的概率之和为1
        for col in range(33):
            col_sum = np.sum(self.red_follow_matrix[:, col])
            if col_sum > 0:
                self.red_follow_matrix[:, col] /= col_sum
        
        # 蓝球跟随概率矩阵：每列的概率之和为1
        for col in range(16):
            col_sum = np.sum(self.blue_follow_matrix[:, col])
            if col_sum > 0:
                self.blue_follow_matrix[:, col] /= col_sum
    
    def _calculate_historical_probabilities(self):
        """计算历史出现概率"""
        from collections import Counter
        
        # 红球历史出现概率
        red_counts = Counter()
        for i in range(1, 7):
            red_counts.update(self.database[f'r{i}'].tolist())
        
        total_red = sum(red_counts.values())
        self.red_ball_probabilities = {}
        for ball in range(1, 34):
            count = red_counts.get(ball, 0)
            self.red_ball_probabilities[ball] = count / total_red if total_red > 0 else 0
        
        # 蓝球历史出现概率
        blue_counts = Counter(self.database['b'].tolist())
        total_blue = sum(blue_counts.values())
        self.blue_ball_probabilities = {}
        for ball in range(1, 17):
            count = blue_counts.get(ball, 0)
            self.blue_ball_probabilities[ball] = count / total_blue if total_blue > 0 else 0
    
    def _calculate_markov_probabilities(self):
        """计算马尔科夫链概率"""
        if self.latest_period is None:
            return
        
        # 获取最新一期的红球和蓝球号码
        latest_red_balls = self.latest_period['red_balls']  # s1, s2, s3, s4, s5, s6
        latest_blue_ball = self.latest_period['blue_ball']  # s7
        
        # 计算红球马尔科夫链概率
        self._calculate_red_markov_probabilities(latest_red_balls)
        
        # 计算蓝球马尔科夫链概率
        self._calculate_blue_markov_probabilities(latest_blue_ball)
    
    def _calculate_red_markov_probabilities(self, latest_red_balls: List[int]):
        """
        计算红球马尔科夫链概率
        
        Args:
            latest_red_balls: 最新一期的红球号码列表
        """
        # 构建新矩阵1：33行6列
        # 从跟随性概率矩阵中提取最新一期红球号码相关的列
        matrix1 = np.zeros((33, 6))
        for i, ball in enumerate(latest_red_balls):
            if 1 <= ball <= 33:
                matrix1[:, i] = self.red_follow_matrix[:, ball - 1]
        
        # 构建新向量2：6行1列
        # 从历史出现概率中提取最新一期红球号码相关的概率
        vector2 = np.zeros((6, 1))
        for i, ball in enumerate(latest_red_balls):
            if 1 <= ball <= 33:
                vector2[i, 0] = self.red_ball_probabilities.get(ball, 0)
        
        # 计算马尔科夫链列向量：matrix1 × vector2
        self.red_markov_probabilities = np.dot(matrix1, vector2).flatten()
    
    def _calculate_blue_markov_probabilities(self, latest_blue_ball: int):
        """
        计算蓝球马尔科夫链概率
        
        Args:
            latest_blue_ball: 最新一期的蓝球号码
        """
        # 蓝球马尔科夫链概率直接等于跟随性概率矩阵中对应列
        if 1 <= latest_blue_ball <= 16:
            self.blue_markov_probabilities = self.blue_follow_matrix[:, latest_blue_ball - 1].copy()
        else:
            self.blue_markov_probabilities = np.zeros(16)
    
    def get_probability_tables(self) -> Dict:
        """
        获取马尔科夫链概率表格
        
        Returns:
            概率表格字典
        """
        return {
            'red_markov_probabilities': self.red_markov_probabilities,
            'blue_markov_probabilities': self.blue_markov_probabilities,
            'red_follow_matrix': self.red_follow_matrix,
            'blue_follow_matrix': self.blue_follow_matrix
        }
    
    def get_top_red_balls_by_markov(self, count: int) -> List[int]:
        """
        根据马尔科夫链概率获取前N个红球号码
        
        Args:
            count: 需要获取的红球数量
            
        Returns:
            红球号码列表
        """
        if self.red_markov_probabilities is None:
            return []
        
        # 获取概率最大的前count个红球
        indices = np.argsort(self.red_markov_probabilities)[::-1][:count]
        return [idx + 1 for idx in indices]
    
    def get_top_blue_balls_by_markov(self, count: int) -> List[int]:
        """
        根据马尔科夫链概率获取前N个蓝球号码
        
        Args:
            count: 需要获取的蓝球数量
            
        Returns:
            蓝球号码列表
        """
        if self.blue_markov_probabilities is None:
            return []
        
        # 获取概率最大的前count个蓝球
        indices = np.argsort(self.blue_markov_probabilities)[::-1][:count]
        return [idx + 1 for idx in indices]
    
    def get_red_markov_probability(self, ball: int) -> float:
        """
        获取指定红球的马尔科夫链概率
        
        Args:
            ball: 红球号码
            
        Returns:
            概率值
        """
        if self.red_markov_probabilities is None or not (1 <= ball <= 33):
            return 0.0
        
        return self.red_markov_probabilities[ball - 1]
    
    def get_blue_markov_probability(self, ball: int) -> float:
        """
        获取指定蓝球的马尔科夫链概率
        
        Args:
            ball: 蓝球号码
            
        Returns:
            概率值
        """
        if self.blue_markov_probabilities is None or not (1 <= ball <= 16):
            return 0.0
        
        return self.blue_markov_probabilities[ball - 1]
