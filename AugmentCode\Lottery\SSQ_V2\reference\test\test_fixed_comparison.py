#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的比对引擎
验证数据类型一致性和调试信息
"""

import sys
import numpy as np
from modules.comparison_engine import ComparisonEngine


def test_fixed_comparison_engine():
    """测试修正后的比对引擎"""
    print("=== 测试修正后的比对引擎 ===")
    
    # 模拟包含numpy类型的预测数据
    predictions = {
        1: {
            'red_balls': [2, 7, 10, 22, 27, 33],  # Python list
            'blue_ball': 14,
            'method': '测试方法1'
        },
        2: {
            'red_balls': [np.int64(2), np.int64(7), np.int64(10), np.int64(22), np.int64(27), np.int64(33)],  # numpy类型
            'blue_ball': np.int64(14),
            'method': '测试方法2'
        }
    }
    
    # 答案数据
    answer_data = [
        {
            'period': '25073',
            'red_balls': [2, 7, 10, 27, 30, 33],
            'blue_ball': 11
        }
    ]
    
    print("测试数据:")
    print(f"  预测1: {predictions[1]['red_balls']} + {predictions[1]['blue_ball']} (Python类型)")
    print(f"  预测2: {predictions[2]['red_balls']} + {predictions[2]['blue_ball']} (numpy类型)")
    print(f"  答案: {answer_data[0]['red_balls']} + {answer_data[0]['blue_ball']}")
    
    # 使用修正后的比对引擎
    config = {}
    engine = ComparisonEngine(config)
    
    print(f"\n执行比对:")
    comparison_results = engine.compare_predictions(predictions, answer_data)
    
    print(f"\n比对结果验证:")
    
    expected_red_hits = 5
    expected_blue_hits = 0
    expected_total_hits = 5
    
    all_correct = True
    
    for group_id in [1, 2]:
        if group_id in comparison_results:
            group_result = comparison_results[group_id]
            max_hit = group_result['max_hit']
            
            print(f"\n第{group_id}组结果:")
            print(f"  最大命中: {max_hit['total_hits']}球")
            print(f"  红球命中数: {max_hit['red_hits']}")
            print(f"  蓝球命中数: {max_hit['blue_hits']}")
            print(f"  蓝球命中状态: {max_hit['blue_hit_status']}")
            
            if (max_hit['red_hits'] == expected_red_hits and
                max_hit['blue_hits'] == expected_blue_hits and
                max_hit['total_hits'] == expected_total_hits and
                max_hit['blue_hit_status'] == False):
                print(f"  ✅ 第{group_id}组计算正确")
            else:
                print(f"  ❌ 第{group_id}组计算错误")
                print(f"    期望: 红{expected_red_hits}+蓝{expected_blue_hits}={expected_total_hits}球, 蓝球状态False")
                print(f"    实际: 红{max_hit['red_hits']}+蓝{max_hit['blue_hits']}={max_hit['total_hits']}球, 蓝球状态{max_hit['blue_hit_status']}")
                all_correct = False
        else:
            print(f"❌ 第{group_id}组没有比对结果")
            all_correct = False
    
    return all_correct


def test_data_consistency():
    """测试数据一致性"""
    print(f"\n=== 测试数据一致性 ===")
    
    # 测试不同数据类型的一致性
    test_cases = [
        {
            'name': 'Python int vs numpy int64',
            'pred_red': [2, 7, 10, 22, 27, 33],
            'pred_blue': 14,
            'answer_red': [np.int64(2), np.int64(7), np.int64(10), np.int64(27), np.int64(30), np.int64(33)],
            'answer_blue': np.int64(11)
        },
        {
            'name': 'numpy int64 vs Python int',
            'pred_red': [np.int64(2), np.int64(7), np.int64(10), np.int64(22), np.int64(27), np.int64(33)],
            'pred_blue': np.int64(14),
            'answer_red': [2, 7, 10, 27, 30, 33],
            'answer_blue': 11
        }
    ]
    
    config = {}
    engine = ComparisonEngine(config)
    
    all_passed = True
    
    for case in test_cases:
        print(f"\n--- {case['name']} ---")
        
        prediction = {
            'red_balls': case['pred_red'],
            'blue_ball': case['pred_blue'],
            'method': case['name']
        }
        
        answer_data = [{
            'period': '25073',
            'red_balls': case['answer_red'],
            'blue_ball': case['answer_blue']
        }]
        
        try:
            results = engine._compare_single_prediction(prediction, answer_data)
            
            if results:
                result = results[0]
                print(f"  结果: 红{result['red_hits']}+蓝{result['blue_hits']}={result['total_hits']}球")
                
                if (result['red_hits'] == 5 and 
                    result['blue_hits'] == 0 and 
                    result['total_hits'] == 5):
                    print(f"  ✅ 通过")
                else:
                    print(f"  ❌ 失败")
                    all_passed = False
            else:
                print(f"  ❌ 没有返回结果")
                all_passed = False
                
        except Exception as e:
            print(f"  ❌ 出现异常: {e}")
            all_passed = False
    
    return all_passed


def main():
    """主函数"""
    print("测试修正后的比对引擎")
    print("=" * 60)
    
    success1 = test_fixed_comparison_engine()
    success2 = test_data_consistency()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 修正后的比对引擎测试通过！")
        print("数据类型一致性问题已解决")
        print("所有预测组现在应该产生一致的比对结果")
    else:
        print("❌ 修正后的比对引擎仍有问题")
    
    return success1 and success2


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
