# -*- coding: utf-8 -*-
"""
用户界面模块 (User Interface Module)

负责处理用户交互、输入验证和结果显示。
"""

import re
from typing import Dict, List, Optional


class UserInterface:
    """
    用户界面类
    
    负责所有用户交互功能
    """
    
    def __init__(self):
        """初始化用户界面"""
        pass
    
    def show_welcome(self):
        """显示欢迎信息"""
        print("=" * 60)
        print("          双色球彩票预测与分析系统 V2.0")
        print("=" * 60)
        print("本系统提供以下功能：")
        print("- 历史出现概率分析")
        print("- 马尔科夫链算法")
        print("- 预测选号")
        print("- 分析比对")
        print("=" * 60)
    
    def get_main_menu_choice(self) -> int:
        """获取主菜单选择"""
        while True:
            print("\n请选择功能：")
            print("1. 预测选号")
            print("2. 分析比对")
            print("0. 退出程序")
            
            try:
                choice = input("请输入选择 (0-2): ").strip()
                choice_int = int(choice)
                
                if choice_int in [0, 1, 2]:
                    return choice_int
                else:
                    print("无效选择，请输入 0、1 或 2。")
            except ValueError:
                print("输入格式错误，请输入数字。")
    
    def get_target_period_for_prediction(self, original_database) -> str:
        """获取预测模式的目标期号"""
        while True:
            print("\n请输入目标期号（预测基于该期号及之前的数据）：")
            print("输入 0 表示使用原始数据库中的最新1期")
            print("期号格式：5位数字，如 25001 表示2025年第1期")

            period_input = input("请输入期号: ").strip()

            # 检查是否输入0
            if period_input == "0":
                # 返回原始数据库中的最新期号
                latest_period = str(max(original_database['NO']))
                print(f"使用原始数据库最新期号: {latest_period}")
                return latest_period

            # 验证期号格式
            if re.match(r'^\d{5}$', period_input):
                # 验证期号是否存在于原始数据库中
                period_int = int(period_input)
                if period_int in original_database['NO'].values:
                    return period_input
                else:
                    print(f"期号 {period_input} 在原始数据库中不存在，请重新输入。")
            else:
                print("期号格式错误，请输入5位数字或0。")
    
    def get_target_period_for_analysis(self) -> str:
        """获取分析模式的目标期号"""
        while True:
            print("\n请输入要开始分析的期号：")
            print("期号格式：5位数字，如 25001 表示2025年第1期")
            
            period_input = input("请输入期号: ").strip()
            
            # 验证期号格式
            if re.match(r'^\d{5}$', period_input):
                return period_input
            else:
                print("期号格式错误，请输入5位数字。")
    
    def get_long_term_database_range(self) -> int:
        """获取远期数据库范围"""
        while True:
            print("\n请指定远期数据库范围：")
            print("输入 0 表示使用全部历史数据")
            print("输入正整数表示使用最近N期数据（如：500表示最近500期）")
            
            try:
                range_input = input("请输入远期数据范围: ").strip()
                range_int = int(range_input)
                
                if range_int >= 0:
                    return range_int
                else:
                    print("请输入非负整数。")
            except ValueError:
                print("输入格式错误，请输入数字。")
    
    def get_short_term_database_range(self, long_term_range: int) -> int:
        """获取近期数据库范围"""
        while True:
            print("\n请指定近期数据库范围：")
            print("需要输入一个正整数，如：20表示最近20期")
            if long_term_range > 0:
                print(f"注意：近期数据范围不能大于远期数据范围({long_term_range})")
            
            try:
                range_input = input("请输入近期数据范围: ").strip()
                range_int = int(range_input)
                
                if range_int <= 0:
                    print("近期数据范围必须是正整数。")
                elif long_term_range > 0 and range_int > long_term_range:
                    print(f"近期数据范围不能大于远期数据范围({long_term_range})。")
                else:
                    return range_int
            except ValueError:
                print("输入格式错误，请输入数字。")
    
    def show_latest_period_info(self, latest_period: Dict, database):
        """显示最新一期信息"""
        if latest_period is None:
            print("无法获取最新一期信息。")
            return
        
        print(f"\n=== 最新一期信息 ===")
        red_balls_str = ' '.join(f"{ball:2d}" for ball in latest_period['red_balls'])
        print(f"最新一期的号码为：{latest_period['period']} {red_balls_str} + {latest_period['blue_ball']:2d}")
        
        # 显示数据库信息
        print(f"当前数据库包含 {len(database)} 期数据")
        start_period = min(database['NO'])
        end_period = max(database['NO'])
        print(f"数据范围：{start_period} - {end_period}")
    
    def show_prediction_results(self, predictions: Dict):
        """显示预测结果"""
        print(f"\n=== 预测结果 ===")
        
        for group_id, prediction in predictions.items():
            red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
            blue_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['blue_balls'])
            
            print(f"第{group_id}组预测的号码为：{red_balls_str} + {blue_balls_str} {prediction['method']}")
    
    def show_analysis_progress(self, completed: int, total: int,
                             long_term_database_size: int, short_term_database_size: int,
                             latest_period: Dict):
        """显示分析进度"""
        print(f"\n=== 分析进度 ===")
        print(f"已完成 {completed}/{total} 期分析")
        print(f"当前远期数据库包含 {long_term_database_size} 期数据")
        print(f"当前近期数据库包含 {short_term_database_size} 期数据")

        if latest_period:
            red_balls_str = ' '.join(f"{ball:2d}" for ball in latest_period['red_balls'])
            print(f"当前最新1期的号码：{latest_period['period']} {red_balls_str} + {latest_period['blue_ball']:2d}")
    
    def show_analysis_start_info(self, total_periods: int):
        """显示分析开始信息"""
        print(f"\n=== 分析比对开始 ===")
        print(f"需要分析比对的总期数：{total_periods}")
        print("开始进行分析比对...")

    def show_final_analysis_results(self, results: List[Dict]):
        """显示最终分析结果"""
        print(f"\n=== 最终分析结果 ===")
        print(f"总共分析了 {len(results)} 期数据")

        # 统计每组预测号码的最大命中情况分布
        group_hit_distribution = {}

        for result in results:
            for group_id, comparison in result['comparison'].items():
                if group_id not in group_hit_distribution:
                    group_hit_distribution[group_id] = {}

                max_hit = comparison['max_hit_count']
                if max_hit not in group_hit_distribution[group_id]:
                    group_hit_distribution[group_id][max_hit] = 0
                group_hit_distribution[group_id][max_hit] += 1

        print("\n每一组预测号码的最大命中情况分布统计结果：")
        for group_id in sorted(group_hit_distribution.keys()):
            print(f"\n第{group_id}组预测号码最大命中情况分布：")
            hit_dist = group_hit_distribution[group_id]
            for hit_count in sorted(hit_dist.keys(), reverse=True):
                count = hit_dist[hit_count]
                print(f"  {hit_count}球命中：{count}次")

        # 总体命中分布统计
        total_hit_distribution = {}
        for result in results:
            for group_id, comparison in result['comparison'].items():
                max_hit = comparison['max_hit_count']
                if max_hit not in total_hit_distribution:
                    total_hit_distribution[max_hit] = 0
                total_hit_distribution[max_hit] += 1

        print(f"\n总体命中分布统计：")
        for hit_count in sorted(total_hit_distribution.keys(), reverse=True):
            count = total_hit_distribution[hit_count]
            print(f"{hit_count}球命中：{count}次")
    
    def ask_save_results(self) -> bool:
        """询问是否保存结果"""
        while True:
            choice = input("\n是否保存统计表格？(y/n): ").strip().lower()
            if choice in ['y', 'yes', '是']:
                return True
            elif choice in ['n', 'no', '否']:
                return False
            else:
                print("请输入 y 或 n。")
