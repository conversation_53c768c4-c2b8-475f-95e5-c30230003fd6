#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球彩票预测与分析系统测试脚本

用于测试系统的基本功能
"""

import sys
import os
import pandas as pd
import numpy as np

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.data_loader import DataLoader
from modules.statistical_analyzer import StatisticalAnalyzer
from modules.markov_chain import MarkovChainAnalyzer
from modules.prediction_engine import PredictionEngine
from modules.comparison_engine import ComparisonEngine
from modules.export_manager import ExportManager


def test_data_loader():
    """测试数据加载器"""
    print("=== 测试数据加载器 ===")
    
    try:
        # 检查数据文件是否存在
        if not os.path.exists("lottery_data_all.xlsx"):
            print("数据文件不存在，创建模拟数据进行测试...")
            create_mock_data()
        
        loader = DataLoader("lottery_data_all.xlsx")
        print(f"数据加载成功，共 {len(loader.original_database)} 期数据")
        
        # 测试获取指定期号的数据
        test_period = "25050"
        database = loader.get_database_for_period(test_period, 100)
        if database is not None:
            print(f"获取期号 {test_period} 及之前的100期数据成功，共 {len(database)} 期")
        
        # 测试获取最新一期信息
        latest = loader.get_latest_period(database)
        if latest:
            print(f"最新一期：{latest['period']} {latest['red_balls']} + {latest['blue_ball']}")
        
        return True
        
    except Exception as e:
        print(f"数据加载器测试失败: {e}")
        return False


def test_statistical_analyzer():
    """测试统计分析器"""
    print("\n=== 测试统计分析器 ===")
    
    try:
        config = {
            'red_ball_range': (1, 33),
            'blue_ball_range': (1, 16),
            'red_ball_count': 6,
            'blue_ball_count': 1
        }
        
        # 创建模拟数据
        data = create_mock_database(50)
        
        analyzer = StatisticalAnalyzer(config)
        analyzer.analyze(data)
        
        # 测试获取概率表格
        prob_tables = analyzer.get_probability_tables()
        print("统计分析完成")
        print(f"红球概率表格大小: {len(prob_tables['red_ball_probabilities'])}")
        print(f"蓝球概率表格大小: {len(prob_tables['blue_ball_probabilities'])}")
        
        # 测试获取前N个红球
        top_red = analyzer.get_top_red_balls_by_probability(6)
        print(f"概率最高的6个红球: {top_red}")
        
        return True
        
    except Exception as e:
        print(f"统计分析器测试失败: {e}")
        return False


def test_markov_analyzer():
    """测试马尔科夫链分析器"""
    print("\n=== 测试马尔科夫链分析器 ===")
    
    try:
        config = {
            'red_ball_range': (1, 33),
            'blue_ball_range': (1, 16),
            'red_ball_count': 6,
            'blue_ball_count': 1
        }
        
        # 创建模拟数据
        data = create_mock_database(50)
        latest_period = {
            'period': '25050',
            'red_balls': [1, 5, 10, 15, 20, 25],
            'blue_ball': 8
        }
        
        analyzer = MarkovChainAnalyzer(config)
        analyzer.analyze(data, latest_period)
        
        # 测试获取概率表格
        prob_tables = analyzer.get_probability_tables()
        print("马尔科夫链分析完成")
        
        if prob_tables['red_markov_probabilities'] is not None:
            print(f"红球马尔科夫概率向量大小: {len(prob_tables['red_markov_probabilities'])}")
        
        if prob_tables['blue_markov_probabilities'] is not None:
            print(f"蓝球马尔科夫概率向量大小: {len(prob_tables['blue_markov_probabilities'])}")
        
        # 测试获取前N个红球
        top_red = analyzer.get_top_red_balls_by_markov(6)
        print(f"马尔科夫概率最高的6个红球: {top_red}")
        
        return True
        
    except Exception as e:
        print(f"马尔科夫链分析器测试失败: {e}")
        return False


def create_mock_database(num_periods: int) -> pd.DataFrame:
    """创建模拟数据库"""
    data = []
    
    for i in range(num_periods):
        period = 25001 + i
        
        # 生成随机红球（6个不重复的1-33之间的数字）
        red_balls = sorted(np.random.choice(range(1, 34), 6, replace=False))
        
        # 生成随机蓝球（1个1-16之间的数字）
        blue_ball = np.random.randint(1, 17)
        
        row = {'NO': period}
        for j, ball in enumerate(red_balls):
            row[f'r{j+1}'] = ball
        row['b'] = blue_ball
        
        data.append(row)
    
    return pd.DataFrame(data)


def create_mock_data():
    """创建模拟Excel数据文件"""
    print("创建模拟数据文件...")
    
    # 创建模拟数据
    data = []
    for i in range(200):  # 创建200期数据
        period = 25001 + i
        
        # 生成随机红球
        red_balls = sorted(np.random.choice(range(1, 34), 6, replace=False))
        
        # 生成随机蓝球
        blue_ball = np.random.randint(1, 17)
        
        # 创建完整行数据（包含其他列）
        row = [None] * 15  # A到O列
        row[0] = period  # A列：期号
        
        # I到N列：红球
        for j, ball in enumerate(red_balls):
            row[8 + j] = ball  # I列开始（索引8）
        
        # O列：蓝球
        row[14] = blue_ball
        
        data.append(row)
    
    # 创建DataFrame
    columns = [chr(65 + i) for i in range(15)]  # A到O列
    df = pd.DataFrame(data, columns=columns)
    
    # 保存到Excel文件
    with pd.ExcelWriter("lottery_data_all.xlsx", engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='SSQ_data_all', index=False)
    
    print("模拟数据文件创建完成")


def main():
    """主测试函数"""
    print("双色球彩票预测与分析系统 - 功能测试")
    print("=" * 50)
    
    # 测试各个模块
    tests = [
        ("数据加载器", test_data_loader),
        ("统计分析器", test_statistical_analyzer),
        ("马尔科夫链分析器", test_markov_analyzer),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name}测试出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    for test_name, result in results:
        status = "通过" if result else "失败"
        print(f"{test_name}: {status}")
    
    # 总体结果
    all_passed = all(result for _, result in results)
    print(f"\n总体测试结果: {'全部通过' if all_passed else '存在失败'}")
    
    if all_passed:
        print("\n系统基本功能正常，可以运行主程序！")
        print("运行命令: python ssq_lottery_system.py")


if __name__ == "__main__":
    main()
