#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找25067期匹配用户号码的预测组
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def find_25067_matching_group():
    """查找25067期匹配用户号码的预测组"""
    print("=== 查找25067期匹配用户号码的预测组 ===")
    
    # 用户提到的号码
    user_red_balls = [2, 7, 10, 22, 27, 33]
    user_blue_ball = 14
    
    print(f"用户提到的号码: {user_red_balls} + {user_blue_ball}")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 用户的参数
    start_period = '25001'
    database_range = 99
    
    try:
        # 获取分析期号列表
        analysis_periods = system.data_loader.get_analysis_periods(start_period)
        
        # 找到25067期的索引
        target_period = '25067'
        target_index = None
        
        for i, period in enumerate(analysis_periods):
            if period == target_period:
                target_index = i
                break
        
        if target_index is None:
            print(f"❌ 未找到期号 {target_period}")
            return False
        
        print(f"找到25067期，索引: {target_index}")
        
        # 获取25067期的数据库
        current_database = system.data_loader.get_database_for_period(
            target_period, database_range
        )
        
        # 获取答案数据
        answer_data = system.data_loader.get_answer_data(target_period)
        
        # 运行预测
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 分析
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = system.prediction_engine.generate_all_predictions(
            current_database, latest_period
        )
        
        print(f"\n=== 25067期所有预测组 ===")
        
        matching_group = None
        
        for group_id in range(1, 25):
            if group_id in predictions:
                prediction = predictions[group_id]
                red_balls = prediction['red_balls']
                blue_ball = prediction['blue_ball']
                method = prediction['method']
                
                print(f"第{group_id}组: {red_balls} + {blue_ball} ({method})")
                
                # 检查是否匹配用户的号码
                if (red_balls == user_red_balls and blue_ball == user_blue_ball):
                    matching_group = group_id
                    print(f"  ✅ 匹配用户号码！")
        
        if matching_group is None:
            print(f"\n❌ 未找到匹配用户号码的预测组")
            print(f"用户号码: {user_red_balls} + {user_blue_ball}")
            print(f"可能的原因：")
            print(f"1. 用户查看的是不同期号的预测")
            print(f"2. 用户查看的是手动修改过的预测")
            print(f"3. 预测算法已经更新")
            return False
        
        print(f"\n=== 第{matching_group}组详细比对 ===")
        
        # 比对结果
        comparison_result = system.comparison_engine.compare_predictions(
            predictions, answer_data
        )
        
        if matching_group in comparison_result:
            group_result = comparison_result[matching_group]
            max_hit = group_result['max_hit']
            
            print(f"预测号码: {predictions[matching_group]['red_balls']} + {predictions[matching_group]['blue_ball']}")
            print(f"预测方法: {predictions[matching_group]['method']}")
            print(f"最大命中: {max_hit['total_hits']}球")
            print(f"最大命中期号: {max_hit['period']}")
            print(f"红球命中数: {max_hit['red_hits']}")
            print(f"蓝球命中数: {max_hit['blue_hits']}")
            
            if max_hit['period'] == '25073':
                print(f"实际号码: {max_hit['answer_red_balls']} + {max_hit['answer_blue_ball']}")
                
                # 手动验证
                pred_red_set = set(user_red_balls)
                answer_red_set = set(max_hit['answer_red_balls'])
                red_hits_manual = len(pred_red_set & answer_red_set)
                blue_hits_manual = 1 if user_blue_ball == max_hit['answer_blue_ball'] else 0
                total_hits_manual = red_hits_manual + blue_hits_manual
                
                print(f"\n手动验证:")
                print(f"  预测红球集合: {pred_red_set}")
                print(f"  实际红球集合: {answer_red_set}")
                print(f"  红球交集: {pred_red_set & answer_red_set}")
                print(f"  手动计算红球命中数: {red_hits_manual}")
                print(f"  手动计算蓝球命中数: {blue_hits_manual}")
                print(f"  手动计算总命中数: {total_hits_manual}")
                
                if max_hit['total_hits'] == total_hits_manual == 5:
                    print(f"  ✅ 第{matching_group}组计算正确: 5球命中")
                    return True
                elif max_hit['total_hits'] == 6 and total_hits_manual == 5:
                    print(f"  ❌ 第{matching_group}组计算错误: 系统显示6球，应该是5球")
                    return False
                else:
                    print(f"  ⚠️ 意外结果: 系统{max_hit['total_hits']}球，手动{total_hits_manual}球")
                    return False
            else:
                print(f"  ⚠️ 最大命中期号不是25073，而是{max_hit['period']}")
                return False
        else:
            print(f"❌ 第{matching_group}组没有比对结果")
            return False
        
    except Exception as e:
        print(f"查找过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("查找25067期匹配用户号码的预测组")
    print("=" * 60)
    
    success = find_25067_matching_group()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 找到匹配的预测组，命中数计算正确！")
    else:
        print("❌ 未找到匹配的预测组或计算有误")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
