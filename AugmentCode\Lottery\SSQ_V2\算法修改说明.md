# 双色球预测算法修改说明

## 修改概述

根据用户需求，对双色球预测算法进行了重要修改，主要变化是调整了远期和近期数据库的号码选择策略，确保近期选择的号码与远期选择的号码不重复。

## 修改前算法

### 第1组：历史出现概率复式
- **远期数据库**：选择红球历史出现概率最大的6个红球号码 + 蓝球历史出现概率最大的1个蓝球号码
- **近期数据库**：选择红球历史出现概率最大的2个红球号码 + 蓝球历史出现概率最大的1个蓝球号码
- **合并方式**：去重后合并，可能导致总数少于预期

### 第2组：马尔科夫链复式
- **远期数据库**：选择红球迁移概率最大的6个红球号码 + 蓝球迁移概率最大的1个蓝球号码
- **近期数据库**：选择红球迁移概率最大的2个红球号码 + 蓝球迁移概率最大的1个蓝球号码
- **合并方式**：去重后合并，可能导致总数少于预期

## 修改后算法

### 第1组：历史出现概率复式
1. **远期数据库选择**：
   - 选择红球历史出现概率最大的6个红球号码
   - 选择蓝球历史出现概率最大的1个蓝球号码

2. **近期数据库选择**：
   - 选择与远期红球不重复的红球历史出现概率最大的1个红球号码
   - 选择与远期蓝球不重复的蓝球历史出现概率最大的1个蓝球号码

3. **合并方式**：
   - 直接合并（不去重，因为已确保不重复）
   - 按从小到大排序
   - 最终结果：7个红球 + 2个蓝球

### 第2组：马尔科夫链复式
1. **远期数据库选择**：
   - 选择红球迁移概率最大的6个红球号码
   - 选择蓝球迁移概率最大的1个蓝球号码

2. **近期数据库选择**：
   - 选择与远期红球不重复的红球迁移概率最大的1个红球号码
   - 选择与远期蓝球不重复的蓝球迁移概率最大的1个蓝球号码

3. **合并方式**：
   - 直接合并（不去重，因为已确保不重复）
   - 按从小到大排序
   - 最终结果：7个红球 + 2个蓝球

## 核心代码修改

### 历史出现概率复式算法修改

```python
# 修改前：简单选择前N个
short_term_red_balls = [ball for ball, prob in short_term_red_sorted[:2]]

# 修改后：选择与远期不重复的前1个
short_term_red_balls = []
for ball, prob in short_term_red_sorted:
    if ball not in long_term_red_balls:
        short_term_red_balls.append(ball)
        break  # 只选1个
```

### 马尔科夫链复式算法修改

```python
# 修改前：简单选择前N个
short_term_red_indices = np.argsort(short_term_red_markov)[::-1][:2]
short_term_red_balls = [idx + 1 for idx in short_term_red_indices]

# 修改后：选择与远期不重复的前1个
short_term_red_balls = []
if short_term_red_markov is not None:
    short_term_red_indices = np.argsort(short_term_red_markov)[::-1]
    for idx in short_term_red_indices:
        ball = idx + 1
        if ball not in long_term_red_balls:
            short_term_red_balls.append(ball)
            break  # 只选1个
```

## 修改影响的文件

### 1. 核心算法文件
- `modules/prediction_engine.py`：主要修改文件，包含两个预测算法的实现

### 2. 文档文件
- `README.md`：更新预测算法说明
- `使用指南.md`：更新预测算法详细说明
- `项目开发总结.md`：更新算法实现描述

## 修改验证

### 测试结果
运行演示程序验证修改效果：

```
=== 预测结果 ===
第1组预测的号码为： 2  7 10 17 18 27 33 +  1 15 历史出现概率复式
第2组预测的号码为： 3  6  7  8 10 13 33 +  5 14 马尔科夫链复式
```

### 结果分析
- **第1组**：7个红球（2,7,10,17,18,27,33）+ 2个蓝球（1,15）
- **第2组**：7个红球（3,6,7,8,10,13,33）+ 2个蓝球（5,14）
- **验证通过**：每组都包含7个红球和2个蓝球，符合新算法要求

## 算法优势

### 1. 确保号码多样性
- 避免了远期和近期选择相同号码的情况
- 保证了复式投注的号码覆盖面

### 2. 结果可预期
- 每组预测固定包含7个红球和2个蓝球
- 不会因为去重导致号码数量不足

### 3. 算法逻辑清晰
- 明确的选择顺序：先远期后近期
- 明确的不重复约束：近期必须与远期不同

## 使用建议

### 1. 投注策略
- 可以选择部分红球和蓝球进行投注
- 建议根据个人预算选择合适的投注组合

### 2. 参数设置
- **远期数据范围**：建议500-1000期，确保统计稳定性
- **近期数据范围**：建议20-50期，捕捉近期趋势

### 3. 结果解读
- 关注两组预测的共同号码，可能具有较高的出现概率
- 观察历史命中分布，评估算法效果

## 注意事项

1. **数据依赖**：算法效果依赖于历史数据的质量和完整性
2. **概率特性**：预测结果基于历史统计，不保证未来中奖
3. **理性投注**：请根据个人经济能力理性选择投注金额

## 总结

本次算法修改成功实现了用户需求，确保了预测结果的号码不重复性和数量稳定性。修改后的算法逻辑更加清晰，结果更加可预期，为用户提供了更好的预测体验。
