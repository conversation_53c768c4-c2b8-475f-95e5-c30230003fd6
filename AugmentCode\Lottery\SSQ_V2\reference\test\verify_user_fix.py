# -*- coding: utf-8 -*-
"""
验证用户问题修复
模拟用户运行主程序分析比对的完整流程，验证问题是否已解决
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ssq_lottery_system import SSQLotterySystem


def verify_user_fix():
    """验证用户问题修复"""
    print("=== 验证用户问题修复 ===")
    print("模拟用户运行主程序分析比对的完整流程")
    
    try:
        # 创建系统实例
        system = SSQLotterySystem("lottery_data_all.xlsx")
        
        # 模拟用户选择分析比对模式
        print(f"\n选择模式: 分析比对模式")
        
        # 模拟用户输入期号25067
        target_period = "25067"
        print(f"输入目标期号: {target_period}")
        
        # 模拟用户输入数据库范围99
        database_range = 99
        print(f"输入数据库范围: {database_range}")
        
        # 调用主程序的分析比对方法
        print(f"\n开始运行分析比对...")
        
        # 获取所有可分析的期号（只分析25067这一期）
        analysis_periods = [target_period]
        
        print(f"需要分析比对的总期数: {len(analysis_periods)}")
        
        results = []
        
        for i, period in enumerate(analysis_periods):
            print(f"\n正在分析期号: {period}")
            
            # 获取当前数据库
            current_database = system.data_loader.get_database_for_period(
                period, database_range
            )
            
            if current_database is None or len(current_database) == 0:
                print("❌ 数据库为空")
                continue
            
            print(f"数据库大小: {len(current_database)}")
            
            # 获取答案数据
            answer_data = system.data_loader.get_answer_data(period)
            
            if answer_data is None or len(answer_data) == 0:
                print("❌ 答案数据为空")
                continue
            
            print(f"答案数据期数: {len(answer_data)}")
            
            # 运行预测
            latest_period = system.data_loader.get_latest_period(current_database)
            
            # 分析
            system.statistical_analyzer.analyze(current_database)
            system.markov_analyzer.analyze(current_database, latest_period)
            system.bayesian_analyzer.analyze(current_database, latest_period)
            
            # 生成预测
            predictions = system.prediction_engine.generate_all_predictions(
                current_database, latest_period
            )
            
            # 比对结果
            comparison_result = system.comparison_engine.compare_predictions(
                predictions, answer_data
            )
            
            results.append({
                'period': period,
                'predictions': predictions,
                'comparison': comparison_result,
                'database_size': len(current_database),
                'latest_period': latest_period
            })
            
            print(f"期号 {period} 分析完成")
        
        # 显示最终统计结果（模拟主程序）
        print(f"\n=== 最终统计结果 ===")
        print(f"总共分析了 {len(results)} 期数据")
        
        # 打印高命中数详细信息（这是用户看到的输出）
        print(f"\n调用 _print_high_hit_details 方法:")
        system._print_high_hit_details(results)
        
        # 验证输出的正确性
        print(f"\n=== 验证输出正确性 ===")
        
        if results:
            result = results[0]
            comparison = result['comparison']
            
            # 检查第9组
            if 9 in comparison:
                group_result = comparison[9]
                max_hit = group_result['max_hit']
                prediction = group_result['prediction']  # 使用修复后的数据源
                
                print(f"第9组验证:")
                print(f"  预测红球: {[int(x) for x in prediction['red_balls']]}")
                print(f"  预测蓝球: {int(prediction['blue_ball'])}")
                print(f"  最大命中期号: {max_hit['period']}")
                print(f"  红球命中数: {max_hit['red_hits']}")
                print(f"  蓝球命中数: {max_hit['blue_hits']}")
                print(f"  总命中数: {max_hit['total_hits']}")
                print(f"  蓝球命中状态: {max_hit['blue_hit_status']}")
                
                # 验证逻辑一致性
                pred_blue = int(prediction['blue_ball'])
                answer_blue = int(max_hit['answer_blue_ball'])
                expected_blue_hits = 1 if pred_blue == answer_blue else 0
                expected_blue_status = expected_blue_hits == 1
                
                print(f"\n逻辑一致性验证:")
                print(f"  预测蓝球: {pred_blue}")
                print(f"  答案蓝球: {answer_blue}")
                print(f"  期望蓝球命中数: {expected_blue_hits}")
                print(f"  实际蓝球命中数: {max_hit['blue_hits']}")
                print(f"  期望蓝球命中状态: {expected_blue_status}")
                print(f"  实际蓝球命中状态: {max_hit['blue_hit_status']}")
                
                if (expected_blue_hits == max_hit['blue_hits'] and 
                    expected_blue_status == max_hit['blue_hit_status']):
                    print("✅ 逻辑一致性验证通过")
                else:
                    print("❌ 逻辑一致性验证失败")
                    return False
                
                # 验证总命中数
                expected_total = max_hit['red_hits'] + max_hit['blue_hits']
                if expected_total == max_hit['total_hits']:
                    print("✅ 总命中数计算正确")
                else:
                    print("❌ 总命中数计算错误")
                    return False
        
        print(f"\n✅ 用户问题修复验证完成！")
        print(f"现在用户运行主程序时应该不会再看到逻辑矛盾的输出。")
        
        return True
        
    except Exception as e:
        print(f"验证过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    verify_user_fix()
