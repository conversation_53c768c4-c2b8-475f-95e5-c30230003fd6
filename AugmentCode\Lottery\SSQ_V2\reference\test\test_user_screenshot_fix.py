#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户截图问题的修正
验证Excel中显示的比对结果是否正确
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def test_user_screenshot_cases():
    """测试用户截图中的具体案例"""
    print("=== 测试用户截图中的具体案例 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    # 用户截图中的案例
    test_cases = [
        {
            'period': '24082',
            'group_id': 3,
            'expected_prediction': [2, 3, 10, 20, 22, 33],
            'expected_blue': 11,
            'expected_max_hit_period': '24086',
            'expected_total_hits': 5,
            'expected_red_hits': 4,
            'expected_blue_hits': 1
        },
        {
            'period': '24091',
            'group_id': 20,
            'expected_prediction': [2, 7, 10, 22, 27, 33],
            'expected_blue': 14,
            'expected_max_hit_period': '24094',
            'expected_total_hits': 5,
            'expected_red_hits': 5,
            'expected_blue_hits': 0
        },
        {
            'period': '25067',
            'group_id': 2,
            'expected_prediction': [2, 7, 10, 22, 27, 33],
            'expected_blue': 14,
            'expected_max_hit_period': '25073',
            'expected_total_hits': 5,
            'expected_red_hits': 5,
            'expected_blue_hits': 0
        }
    ]
    
    print("测试用户截图中的案例:")
    
    all_correct = True
    
    for i, case in enumerate(test_cases):
        print(f"\n--- 案例 {i+1}: 分析期号{case['period']} ---")
        
        try:
            # 获取分析期号列表
            analysis_periods = system.data_loader.get_analysis_periods(case['period'])
            
            if case['period'] not in analysis_periods:
                print(f"❌ 期号{case['period']}不在分析范围内")
                all_correct = False
                continue
            
            # 获取数据库和答案
            current_database = system.data_loader.get_database_for_period(case['period'], 100)
            answer_data = system.data_loader.get_answer_data(case['period'])
            latest_period = system.data_loader.get_latest_period(current_database)
            
            print(f"  数据库大小: {len(current_database)} 期")
            print(f"  答案数据: {len(answer_data)} 期")
            
            # 分析
            system.statistical_analyzer.analyze(current_database)
            system.markov_analyzer.analyze(current_database, latest_period)
            system.bayesian_analyzer.analyze(current_database, latest_period)
            
            # 生成预测
            predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
            
            # 比对结果
            comparison_result = system.comparison_engine.compare_predictions(predictions, answer_data)
            
            # 检查指定组的结果
            if case['group_id'] in predictions and case['group_id'] in comparison_result:
                prediction = predictions[case['group_id']]
                group_result = comparison_result[case['group_id']]
                max_hit = group_result['max_hit']
                
                pred_red = [int(x) for x in prediction['red_balls']]
                pred_blue = int(prediction['blue_ball'])
                
                print(f"  第{case['group_id']}组预测: {pred_red} + {pred_blue}")
                print(f"  预测方法: {prediction['method']}")
                print(f"  最大命中期号: {max_hit['period']}")
                print(f"  最大命中球数: {max_hit['total_hits']}")
                print(f"  红球命中数: {max_hit['red_hits']}")
                print(f"  蓝球命中数: {max_hit['blue_hits']}")
                print(f"  蓝球命中状态: {'是' if max_hit['blue_hits'] == 1 else '否'}")
                
                # 验证结果
                if (pred_red == case['expected_prediction'] and
                    pred_blue == case['expected_blue'] and
                    max_hit['period'] == case['expected_max_hit_period'] and
                    max_hit['total_hits'] == case['expected_total_hits'] and
                    max_hit['red_hits'] == case['expected_red_hits'] and
                    max_hit['blue_hits'] == case['expected_blue_hits']):
                    print(f"  ✅ 案例{i+1}验证通过")
                else:
                    print(f"  ❌ 案例{i+1}验证失败")
                    print(f"    期望预测: {case['expected_prediction']} + {case['expected_blue']}")
                    print(f"    实际预测: {pred_red} + {pred_blue}")
                    print(f"    期望最大命中期号: {case['expected_max_hit_period']}")
                    print(f"    实际最大命中期号: {max_hit['period']}")
                    print(f"    期望命中: 红{case['expected_red_hits']}+蓝{case['expected_blue_hits']}={case['expected_total_hits']}球")
                    print(f"    实际命中: 红{max_hit['red_hits']}+蓝{max_hit['blue_hits']}={max_hit['total_hits']}球")
                    all_correct = False
            else:
                print(f"  ❌ 第{case['group_id']}组没有预测或比对结果")
                all_correct = False
                
        except Exception as e:
            print(f"  ❌ 案例{i+1}处理出错: {e}")
            all_correct = False
    
    return all_correct


def test_period_selection_priority():
    """测试期号选择优先级"""
    print(f"\n=== 测试期号选择优先级 ===")
    
    from modules.comparison_engine import ComparisonEngine
    
    # 构造测试：多个期号有相同命中数
    prediction = {
        'red_balls': [1, 2, 3, 4, 5, 6],
        'blue_ball': 7,
        'method': '测试'
    }
    
    # 答案数据：25002和25004都命中3球
    answer_data = [
        {'period': '25001', 'red_balls': [1, 2, 10, 11, 12, 13], 'blue_ball': 8},  # 2球
        {'period': '25002', 'red_balls': [1, 2, 3, 10, 11, 12], 'blue_ball': 8},  # 3球
        {'period': '25003', 'red_balls': [1, 10, 11, 12, 13, 14], 'blue_ball': 8},  # 1球
        {'period': '25004', 'red_balls': [4, 5, 6, 10, 11, 12], 'blue_ball': 8},  # 3球
        {'period': '25005', 'red_balls': [10, 11, 12, 13, 14, 15], 'blue_ball': 8},  # 0球
        {'period': '25006', 'red_balls': [1, 10, 11, 12, 13, 14], 'blue_ball': 7}   # 2球
    ]
    
    print("测试数据:")
    print(f"  预测: {prediction['red_balls']} + {prediction['blue_ball']}")
    print(f"  答案数据命中情况:")
    
    for answer in answer_data:
        pred_red_set = set(prediction['red_balls'])
        answer_red_set = set(answer['red_balls'])
        red_hits = len(pred_red_set & answer_red_set)
        blue_hits = 1 if prediction['blue_ball'] == answer['blue_ball'] else 0
        total_hits = red_hits + blue_hits
        print(f"    期号{answer['period']}: {total_hits}球 (红{red_hits}+蓝{blue_hits})")
    
    print(f"  期号25002和25004都命中3球，应该选择25002（期号更小）")
    
    # 使用比对引擎
    config = {}
    engine = ComparisonEngine(config)
    
    predictions = {1: prediction}
    comparison_results = engine.compare_predictions(predictions, answer_data)
    
    if 1 in comparison_results:
        group_result = comparison_results[1]
        max_hit = group_result['max_hit']
        
        print(f"\n比对结果:")
        print(f"  选择的期号: {max_hit['period']}")
        print(f"  命中球数: {max_hit['total_hits']}")
        
        if max_hit['period'] == '25002' and max_hit['total_hits'] == 3:
            print(f"  ✅ 正确选择了期号最小的25002")
            return True
        else:
            print(f"  ❌ 选择错误，期望25002，实际{max_hit['period']}")
            return False
    else:
        print(f"❌ 没有比对结果")
        return False


def main():
    """主函数"""
    print("测试用户截图问题的修正")
    print("=" * 60)
    
    success1 = test_period_selection_priority()
    success2 = test_user_screenshot_cases()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 用户截图问题修正成功！")
        print("\n修正总结:")
        print("1. ✅ 期号选择逻辑正确：相同命中数时选择期号最小的")
        print("2. ✅ 比对结果与预测号码匹配")
        print("3. ✅ 最大命中期号选择正确")
        print("4. ✅ 红球、蓝球命中数计算正确")
        print("\n用户可以重新运行主程序生成正确的Excel文件")
    else:
        print("❌ 用户截图问题尚未完全解决")
    
    return success1 and success2


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
