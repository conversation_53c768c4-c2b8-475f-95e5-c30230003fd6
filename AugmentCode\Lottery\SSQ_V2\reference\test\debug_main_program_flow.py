# -*- coding: utf-8 -*-
"""
调试主程序完整流程
模拟用户运行主程序分析比对时的完整流程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ssq_lottery_system import SSQLotterySystem
import copy


def debug_main_program_flow():
    """调试主程序完整流程"""
    print("=== 调试主程序完整流程 ===")
    
    try:
        # 创建系统实例
        system = SSQLotterySystem("lottery_data_all.xlsx")
        
        # 模拟用户选择分析比对模式，期号25067，数据库范围99
        target_period = "25067"
        database_range = 99
        
        print(f"分析期号: {target_period}")
        print(f"数据库范围: {database_range}")
        
        # 获取当前数据库
        current_database = system.data_loader.get_database_for_period(
            target_period, database_range
        )
        
        if current_database is None or len(current_database) == 0:
            print("❌ 数据库为空")
            return False
        
        print(f"数据库大小: {len(current_database)}")
        print(f"数据库期号范围: {current_database.iloc[0]['NO']} - {current_database.iloc[-1]['NO']}")
        
        # 获取答案数据
        answer_data = system.data_loader.get_answer_data(target_period)
        
        if answer_data is None or len(answer_data) == 0:
            print("❌ 答案数据为空")
            return False
        
        print(f"答案数据期数: {len(answer_data)}")
        for answer in answer_data:
            red_str = ' '.join(f"{ball:2d}" for ball in answer['red_balls'])
            print(f"  期号{answer['period']}: {red_str} + {answer['blue_ball']:2d}")
        
        # 运行预测
        latest_period = system.data_loader.get_latest_period(current_database)
        print(f"\n最新期号: {latest_period['period']}")
        
        # 分析
        print(f"\n=== 运行分析器 ===")
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        print(f"\n=== 生成预测 ===")
        predictions = system.prediction_engine.generate_all_predictions(
            current_database, latest_period
        )
        
        # 保存第9组预测的快照
        pred_9_original = copy.deepcopy(predictions[9])
        print(f"第9组原始预测:")
        print(f"  红球: {pred_9_original['red_balls']}")
        print(f"  蓝球: {pred_9_original['blue_ball']}")
        print(f"  方法: {pred_9_original['method']}")
        
        # 比对结果
        print(f"\n=== 比对分析 ===")
        comparison_result = system.comparison_engine.compare_predictions(
            predictions, answer_data
        )
        
        # 检查比对后的预测数据
        pred_9_after_comparison = predictions[9]
        comparison_pred_9 = comparison_result[9]['prediction']
        
        print(f"比对后第9组预测:")
        print(f"  原始predictions[9]红球: {pred_9_after_comparison['red_balls']}")
        print(f"  原始predictions[9]蓝球: {pred_9_after_comparison['blue_ball']}")
        print(f"  比对结果中红球: {comparison_pred_9['red_balls']}")
        print(f"  比对结果中蓝球: {comparison_pred_9['blue_ball']}")
        
        # 检查是否相同对象
        print(f"  是否相同对象: {pred_9_after_comparison is comparison_pred_9}")
        print(f"  内容是否相等: {pred_9_after_comparison == comparison_pred_9}")
        
        # 模拟主程序中的results存储
        print(f"\n=== 模拟results存储 ===")
        results = []
        results.append({
            'period': target_period,
            'predictions': predictions,  # 这里存储的是原始引用
            'comparison': comparison_result,
            'database_size': len(current_database),
            'latest_period': latest_period
        })
        
        # 检查results中的数据
        result_pred_9 = results[0]['predictions'][9]
        result_comparison_pred_9 = results[0]['comparison'][9]['prediction']
        
        print(f"results中第9组预测:")
        print(f"  results['predictions'][9]红球: {result_pred_9['red_balls']}")
        print(f"  results['predictions'][9]蓝球: {result_pred_9['blue_ball']}")
        print(f"  results['comparison'][9]['prediction']红球: {result_comparison_pred_9['red_balls']}")
        print(f"  results['comparison'][9]['prediction']蓝球: {result_comparison_pred_9['blue_ball']}")
        
        # 检查内存地址关系
        print(f"\n=== 内存地址关系 ===")
        print(f"predictions[9] id: {id(predictions[9])}")
        print(f"results[0]['predictions'][9] id: {id(result_pred_9)}")
        print(f"results[0]['comparison'][9]['prediction'] id: {id(result_comparison_pred_9)}")
        print(f"predictions[9] is results[0]['predictions'][9]: {predictions[9] is result_pred_9}")
        print(f"predictions[9] is results[0]['comparison'][9]['prediction']: {predictions[9] is result_comparison_pred_9}")
        
        # 模拟 _print_high_hit_details 的调用
        print(f"\n=== 模拟打印功能 ===")
        max_hit = comparison_result[9]['max_hit']
        
        if max_hit['total_hits'] >= 6:
            # 从results中获取数据（模拟_print_high_hit_details的逻辑）
            result = results[0]
            period = result['period']
            predictions_from_result = result['predictions']
            comparison_from_result = result['comparison']
            
            prediction = predictions_from_result[9]  # 这里可能是问题所在！
            group_result = comparison_from_result[9]
            max_hit = group_result['max_hit']
            
            print(f"打印功能使用的数据:")
            print(f"  prediction红球: {prediction['red_balls']}")
            print(f"  prediction蓝球: {prediction['blue_ball']}")
            print(f"  max_hit期号: {max_hit['period']}")
            print(f"  max_hit红球命中: {max_hit['red_hits']}")
            print(f"  max_hit蓝球命中: {max_hit['blue_hits']}")
            print(f"  max_hit总命中: {max_hit['total_hits']}")
            
            # 格式化输出（模拟实际打印）
            red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
            answer_red_str = ' '.join(f"{ball:2d}" for ball in max_hit['answer_red_balls'])
            
            print(f"\n模拟实际打印输出:")
            print(f"🔥 【{max_hit['total_hits']}球命中】")
            print(f"   分析期号: {period}")
            print(f"   预测组号: 第9组")
            print(f"   预测方法: {prediction['method']}")
            print(f"   预测红球: {red_balls_str}")
            print(f"   预测蓝球: {prediction['blue_ball']:2d}")
            print(f"   最大命中数: {max_hit['total_hits']}球")
            print(f"   最大命中期号: {max_hit['period']}")
            print(f"   命中期号红球: {answer_red_str}")
            print(f"   命中期号蓝球: {max_hit['answer_blue_ball']:2d}")
            print(f"   红球命中数: {max_hit['red_hits']}个")
            print(f"   蓝球命中数: {max_hit['blue_hits']}个")
            print(f"   蓝球命中状态: {'是' if max_hit['blue_hits'] == 1 else '否'}")
        
        # 检查是否存在其他修改点
        print(f"\n=== 检查其他可能的修改点 ===")
        
        # 模拟多次调用（如果主程序中有多次调用的话）
        print("模拟多次调用预测引擎:")
        for i in range(3):
            new_predictions = system.prediction_engine.generate_all_predictions(
                current_database, latest_period
            )
            new_pred_9 = new_predictions[9]
            print(f"  第{i+1}次调用第9组: {new_pred_9['red_balls']} + {new_pred_9['blue_ball']}")
            print(f"    与原始相同: {new_pred_9 == pred_9_original}")
        
        return True
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    debug_main_program_flow()
