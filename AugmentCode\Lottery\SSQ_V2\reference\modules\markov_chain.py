# -*- coding: utf-8 -*-
"""
马尔科夫链分析模块 (Markov Chain Analyzer Module)

负责基于马尔科夫链算法计算号码迁移概率。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional


class MarkovChainAnalyzer:
    """
    马尔科夫链分析器类
    
    负责计算基于最新一期号码的号码迁移概率
    """
    
    def __init__(self, config: Dict):
        """
        初始化马尔科夫链分析器
        
        Args:
            config: 系统配置字典
        """
        self.config = config
        self.database = None
        self.latest_period = None
        
        # 马尔科夫链概率向量
        self.red_markov_probabilities = None
        self.blue_markov_probabilities = None
        
        # 跟随性概率矩阵（从统计分析器获取）
        self.red_follow_matrix = None
        self.blue_follow_matrix = None
        
        # 历史出现概率（从统计分析器获取）
        self.red_historical_probabilities = None
        self.blue_historical_probabilities = None
    
    def analyze(self, database: pd.DataFrame, latest_period: Dict):
        """
        执行马尔科夫链分析
        
        Args:
            database: 数据库DataFrame
            latest_period: 最新一期数据
        """
        self.database = database
        self.latest_period = latest_period
        
        # 首先需要计算跟随性概率矩阵和历史概率
        self._calculate_follow_matrices()
        self._calculate_historical_probabilities()
        
        # 计算马尔科夫链概率
        self._calculate_markov_probabilities()
    
    def _calculate_follow_matrices(self):
        """计算跟随性概率矩阵"""
        # 初始化矩阵
        self.red_follow_matrix = np.zeros((33, 33))
        self.blue_follow_matrix = np.zeros((16, 16))
        
        # 红球跟随性统计
        red_follow_counts = np.zeros((33, 33))
        
        for i in range(1, len(self.database)):
            current_row = self.database.iloc[i]
            prev_row = self.database.iloc[i - 1]
            
            # 统计红球跟随关系
            for j in range(1, 7):
                prev_ball = prev_row[f'r{j}'] - 1  # 转换为0-32索引
                for k in range(1, 7):
                    current_ball = current_row[f'r{k}'] - 1  # 转换为0-32索引
                    red_follow_counts[current_ball, prev_ball] += 1
        
        # 转换为概率
        for col in range(33):
            col_sum = np.sum(red_follow_counts[:, col])
            if col_sum > 0:
                self.red_follow_matrix[:, col] = red_follow_counts[:, col] / col_sum
        
        # 蓝球跟随性统计
        blue_follow_counts = np.zeros((16, 16))
        
        for i in range(1, len(self.database)):
            current_row = self.database.iloc[i]
            prev_row = self.database.iloc[i - 1]
            
            prev_blue = prev_row['b'] - 1  # 转换为0-15索引
            current_blue = current_row['b'] - 1  # 转换为0-15索引
            blue_follow_counts[current_blue, prev_blue] += 1
        
        # 转换为概率
        for col in range(16):
            col_sum = np.sum(blue_follow_counts[:, col])
            if col_sum > 0:
                self.blue_follow_matrix[:, col] = blue_follow_counts[:, col] / col_sum
    
    def _calculate_historical_probabilities(self):
        """计算历史出现概率"""
        from collections import Counter
        
        # 红球历史出现概率
        red_counts = Counter()
        for i in range(1, 7):
            red_counts.update(self.database[f'r{i}'].tolist())
        
        total_red = sum(red_counts.values())
        self.red_historical_probabilities = np.zeros(33)
        for ball in range(1, 34):
            count = red_counts.get(ball, 0)
            self.red_historical_probabilities[ball - 1] = count / total_red if total_red > 0 else 0
        
        # 蓝球历史出现概率
        blue_counts = Counter(self.database['b'].tolist())
        total_blue = sum(blue_counts.values())
        self.blue_historical_probabilities = np.zeros(16)
        for ball in range(1, 17):
            count = blue_counts.get(ball, 0)
            self.blue_historical_probabilities[ball - 1] = count / total_blue if total_blue > 0 else 0
    
    def _calculate_markov_probabilities(self):
        """计算马尔科夫链概率"""
        if self.latest_period is None:
            return
        
        # 红球马尔科夫链计算
        self._calculate_red_markov_probabilities()
        
        # 蓝球马尔科夫链计算
        self._calculate_blue_markov_probabilities()
    
    def _calculate_red_markov_probabilities(self):
        """计算红球马尔科夫链概率"""
        # 获取最新一期的红球号码
        latest_red_balls = self.latest_period['red_balls']
        
        # 构建新矩阵1：提取相关列向量并拼接
        # 矩阵1是33行6列，包含最新一期6个红球对应的列向量
        matrix1 = np.zeros((33, 6))
        for i, ball in enumerate(latest_red_balls):
            ball_index = ball - 1  # 转换为0-32索引
            matrix1[:, i] = self.red_follow_matrix[:, ball_index]
        
        # 构建新向量2：最新一期红球号码对应的历史出现概率
        # 向量2是6行1列
        vector2 = np.zeros((6, 1))
        for i, ball in enumerate(latest_red_balls):
            ball_index = ball - 1  # 转换为0-32索引
            vector2[i, 0] = self.red_historical_probabilities[ball_index]
        
        # 计算马尔科夫链概率向量：矩阵1 × 向量2
        self.red_markov_probabilities = np.dot(matrix1, vector2).flatten()
    
    def _calculate_blue_markov_probabilities(self):
        """计算蓝球马尔科夫链概率"""
        # 获取最新一期的蓝球号码
        latest_blue_ball = self.latest_period['blue_ball']
        
        # 蓝球马尔科夫链概率向量直接等于跟随性概率矩阵中对应的列向量
        ball_index = latest_blue_ball - 1  # 转换为0-15索引
        self.blue_markov_probabilities = self.blue_follow_matrix[:, ball_index].copy()
    
    def get_top_probabilities(self, count: int = 6) -> Tuple[List[int], List[float]]:
        """
        获取概率最高的红球号码
        
        Args:
            count: 需要获取的号码数量
            
        Returns:
            (号码列表, 概率列表)
        """
        if self.red_markov_probabilities is None:
            return [], []
        
        # 获取概率最高的号码索引
        top_indices = np.argsort(self.red_markov_probabilities)[-count:][::-1]
        
        # 转换为号码（1-33）
        top_balls = [idx + 1 for idx in top_indices]
        top_probs = [self.red_markov_probabilities[idx] for idx in top_indices]
        
        return top_balls, top_probs
    
    def get_top_blue_probability(self) -> Tuple[int, float]:
        """
        获取概率最高的蓝球号码
        
        Returns:
            (号码, 概率)
        """
        if self.blue_markov_probabilities is None:
            return 1, 0.0
        
        # 获取概率最高的蓝球索引
        top_index = np.argmax(self.blue_markov_probabilities)
        
        # 转换为号码（1-16）
        top_ball = top_index + 1
        top_prob = self.blue_markov_probabilities[top_index]
        
        return top_ball, top_prob
    
    def get_red_probability(self, ball: int) -> float:
        """
        获取指定红球的马尔科夫链概率
        
        Args:
            ball: 红球号码（1-33）
            
        Returns:
            概率值
        """
        if self.red_markov_probabilities is None or ball < 1 or ball > 33:
            return 0.0
        
        return self.red_markov_probabilities[ball - 1]
    
    def get_blue_probability(self, ball: int) -> float:
        """
        获取指定蓝球的马尔科夫链概率
        
        Args:
            ball: 蓝球号码（1-16）
            
        Returns:
            概率值
        """
        if self.blue_markov_probabilities is None or ball < 1 or ball > 16:
            return 0.0
        
        return self.blue_markov_probabilities[ball - 1]
    
    def get_probability_tables(self) -> Dict:
        """
        获取马尔科夫链概率表格
        
        Returns:
            概率表格字典
        """
        red_prob_dict = {}
        blue_prob_dict = {}
        
        if self.red_markov_probabilities is not None:
            for i in range(33):
                red_prob_dict[i + 1] = self.red_markov_probabilities[i]
        
        if self.blue_markov_probabilities is not None:
            for i in range(16):
                blue_prob_dict[i + 1] = self.blue_markov_probabilities[i]
        
        return {
            'red_markov_probabilities': red_prob_dict,
            'blue_markov_probabilities': blue_prob_dict,
            'red_follow_matrix': self.red_follow_matrix,
            'blue_follow_matrix': self.blue_follow_matrix
        }
    
    def get_markov_vectors(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        获取马尔科夫链概率向量
        
        Returns:
            (红球概率向量, 蓝球概率向量)
        """
        return self.red_markov_probabilities, self.blue_markov_probabilities
