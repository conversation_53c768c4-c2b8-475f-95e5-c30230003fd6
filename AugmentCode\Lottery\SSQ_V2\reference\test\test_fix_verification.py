# -*- coding: utf-8 -*-
"""
验证修复效果
测试比对引擎的引用问题是否已解决
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ssq_lottery_system import SSQLotterySystem
import copy


def test_fix_verification():
    """验证修复效果"""
    print("=== 验证引用问题修复效果 ===")
    
    try:
        # 创建系统实例
        system = SSQLotterySystem("lottery_data_all.xlsx")
        
        # 加载数据
        all_data = system.data_loader.get_current_database()
        
        # 找到期号25067的数据
        target_period = "25067"
        target_index = None
        
        for i, row in all_data.iterrows():
            if str(row['NO']) == target_period:
                target_index = i
                break
        
        if target_index is None:
            print(f"❌ 没有找到期号 {target_period}")
            return False
        
        print(f"✅ 找到期号 {target_period}，索引: {target_index}")
        
        # 获取数据库范围（前99期）
        database_range = 99
        start_index = max(0, target_index - database_range + 1)
        end_index = target_index + 1
        current_database = all_data.iloc[start_index:end_index].copy()
        
        # 获取答案数据（后6期）
        answer_start_index = target_index + 1
        answer_end_index = min(len(all_data), target_index + 7)
        answer_data_df = all_data.iloc[answer_start_index:answer_end_index].copy()
        
        # 转换答案数据格式
        answer_data = []
        for _, row in answer_data_df.iterrows():
            red_balls = [int(row[f'r{i}']) for i in range(1, 7)]
            blue_ball = int(row['b'])
            answer_data.append({
                'period': str(row['NO']),
                'red_balls': red_balls,
                'blue_ball': blue_ball
            })
        
        # 获取最新一期数据
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 运行分析器
        print(f"\n=== 运行分析器 ===")
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        print(f"\n=== 生成预测 ===")
        predictions = system.prediction_engine.generate_all_predictions(current_database, latest_period)
        
        # 保存第9组预测的原始数据
        original_pred_9 = copy.deepcopy(predictions[9])
        print(f"原始第9组预测:")
        print(f"  红球: {original_pred_9['red_balls']}")
        print(f"  蓝球: {original_pred_9['blue_ball']}")
        
        # 进行比对分析
        print(f"\n=== 比对分析 ===")
        comparison_result = system.comparison_engine.compare_predictions(predictions, answer_data)
        
        # 检查修复后的引用情况
        print(f"\n=== 检查修复效果 ===")
        current_pred_9 = predictions[9]
        comparison_pred_9 = comparison_result[9]['prediction']
        
        print(f"当前第9组预测:")
        print(f"  红球: {current_pred_9['red_balls']}")
        print(f"  蓝球: {current_pred_9['blue_ball']}")
        
        print(f"比对结果中的第9组预测:")
        print(f"  红球: {comparison_pred_9['red_balls']}")
        print(f"  蓝球: {comparison_pred_9['blue_ball']}")
        
        # 检查是否还是同一个对象
        print(f"\n=== 对象引用检查 ===")
        is_same_object = predictions[9] is comparison_result[9]['prediction']
        print(f"predictions[9] is comparison_result[9]['prediction']: {is_same_object}")
        
        if is_same_object:
            print("❌ 修复失败！仍然是同一个对象")
            return False
        else:
            print("✅ 修复成功！现在是不同的对象")
        
        # 检查内容是否相等
        content_equal = predictions[9] == comparison_result[9]['prediction']
        print(f"内容是否相等: {content_equal}")
        
        # 检查内存地址
        print(f"predictions[9] id: {id(predictions[9])}")
        print(f"comparison_result[9]['prediction'] id: {id(comparison_result[9]['prediction'])}")
        
        # 模拟修改预测数据，验证是否还会影响比对结果
        print(f"\n=== 验证数据隔离 ===")
        print(f"修改前 predictions[9]['red_balls'][0]: {predictions[9]['red_balls'][0]}")
        print(f"修改前 comparison_result[9]['prediction']['red_balls'][0]: {comparison_result[9]['prediction']['red_balls'][0]}")
        
        # 修改预测数据
        old_value = predictions[9]['red_balls'][0]
        predictions[9]['red_balls'][0] = 99  # 修改为一个明显不同的值
        
        print(f"修改后 predictions[9]['red_balls'][0]: {predictions[9]['red_balls'][0]}")
        print(f"修改后 comparison_result[9]['prediction']['red_balls'][0]: {comparison_result[9]['prediction']['red_balls'][0]}")
        
        if predictions[9]['red_balls'][0] == comparison_result[9]['prediction']['red_balls'][0]:
            print("❌ 修复失败！修改预测数据仍然影响比对结果")
            return False
        else:
            print("✅ 修复成功！数据已隔离，修改预测数据不影响比对结果")
        
        # 恢复原值
        predictions[9]['red_balls'][0] = old_value
        
        # 验证比对结果的正确性
        print(f"\n=== 验证比对结果正确性 ===")
        max_hit = comparison_result[9]['max_hit']
        prediction = comparison_result[9]['prediction']
        
        print(f"第9组比对结果:")
        print(f"  最大命中期号: {max_hit['period']}")
        print(f"  红球命中数: {max_hit['red_hits']}")
        print(f"  蓝球命中数: {max_hit['blue_hits']}")
        print(f"  总命中数: {max_hit['total_hits']}")
        print(f"  蓝球命中状态: {max_hit['blue_hit_status']}")
        
        # 手动验证最大命中期号的比对
        target_answer = None
        for answer in answer_data:
            if answer['period'] == max_hit['period']:
                target_answer = answer
                break
        
        if target_answer:
            pred_red_set = set([int(x) for x in prediction['red_balls']])
            answer_red_set = set(target_answer['red_balls'])
            hit_red_balls = pred_red_set & answer_red_set
            manual_red_hits = len(hit_red_balls)
            
            manual_blue_hits = 1 if int(prediction['blue_ball']) == target_answer['blue_ball'] else 0
            manual_total_hits = manual_red_hits + manual_blue_hits
            
            print(f"\n手动验证期号 {max_hit['period']}:")
            print(f"  预测红球: {sorted([int(x) for x in prediction['red_balls']])}")
            print(f"  答案红球: {sorted(target_answer['red_balls'])}")
            print(f"  命中红球: {sorted(list(hit_red_balls))}")
            print(f"  预测蓝球: {int(prediction['blue_ball'])}")
            print(f"  答案蓝球: {target_answer['blue_ball']}")
            print(f"  手动计算: 红{manual_red_hits}+蓝{manual_blue_hits}={manual_total_hits}")
            print(f"  引擎计算: 红{max_hit['red_hits']}+蓝{max_hit['blue_hits']}={max_hit['total_hits']}")
            
            if (manual_red_hits == max_hit['red_hits'] and 
                manual_blue_hits == max_hit['blue_hits'] and 
                manual_total_hits == max_hit['total_hits']):
                print("✅ 比对引擎计算正确")
            else:
                print("❌ 比对引擎计算错误")
                return False
        
        # 模拟打印功能
        print(f"\n=== 模拟打印功能 ===")
        if max_hit['total_hits'] >= 6:
            red_balls_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
            answer_red_str = ' '.join(f"{ball:2d}" for ball in max_hit['answer_red_balls'])
            
            print(f"🔥 【{max_hit['total_hits']}球命中】")
            print(f"   分析期号: {target_period}")
            print(f"   预测组号: 第9组")
            print(f"   预测方法: {prediction['method']}")
            print(f"   预测红球: {red_balls_str}")
            print(f"   预测蓝球: {prediction['blue_ball']:2d}")
            print(f"   最大命中数: {max_hit['total_hits']}球")
            print(f"   最大命中期号: {max_hit['period']}")
            print(f"   命中期号红球: {answer_red_str}")
            print(f"   命中期号蓝球: {max_hit['answer_blue_ball']:2d}")
            print(f"   红球命中数: {max_hit['red_hits']}个")
            print(f"   蓝球命中数: {max_hit['blue_hits']}个")
            print(f"   蓝球命中状态: {'是' if max_hit['blue_hits'] == 1 else '否'}")
            
            # 显示具体命中的红球
            hit_red_balls = sorted(list(hit_red_balls))
            hit_red_str = ' '.join(f"{ball:2d}" for ball in hit_red_balls)
            print(f"   命中红球详情: {hit_red_str}")
        
        print(f"\n✅ 所有测试通过！引用问题已修复，比对算法正确。")
        return True
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_fix_verification()
