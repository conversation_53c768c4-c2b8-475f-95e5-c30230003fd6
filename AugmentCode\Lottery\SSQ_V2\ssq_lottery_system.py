#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球彩票预测与分析系统 (SSQ Lottery Prediction and Analysis System)
主程序入口文件

基于用户需求开发的双色球彩票预测选号与分析比对程序
实现历史出现概率、马尔科夫链算法等核心功能

作者: AI Assistant
版本: 2.0
日期: 2025-07-26
"""

import sys
import os
from typing import Dict, List, Tuple, Optional
import pandas as pd
import numpy as np
from pathlib import Path

# 导入自定义模块
from modules.data_loader import DataLoader
from modules.statistical_analyzer import StatisticalAnalyzer
from modules.markov_chain import MarkovChainAnalyzer
from modules.prediction_engine import PredictionEngine
from modules.comparison_engine import ComparisonEngine
from modules.export_manager import ExportManager
from modules.user_interface import UserInterface


class SSQLotterySystem:
    """
    双色球彩票预测与分析系统主类
    
    该类整合了所有功能模块，提供统一的接口来执行预测和分析任务
    """
    
    def __init__(self, data_file_path: str = "lottery_data_all.xlsx"):
        """
        初始化系统
        
        Args:
            data_file_path: 数据文件路径
        """
        self.data_file_path = data_file_path
        self.data_loader = None
        self.statistical_analyzer = None
        self.markov_analyzer = None
        self.prediction_engine = None
        self.comparison_engine = None
        self.export_manager = None
        self.ui = UserInterface()
        
        # 系统配置
        self.config = {
            'red_ball_range': (1, 33),      # 红球号码范围
            'blue_ball_range': (1, 16),     # 蓝球号码范围
            'red_ball_count': 6,             # 每期红球数量
            'blue_ball_count': 1,            # 每期蓝球数量
            'answer_periods': 6              # 答案数据期数
        }
        
        self._initialize_system()
    
    def _initialize_system(self):
        """初始化系统各个模块"""
        try:
            # 初始化数据加载器
            self.data_loader = DataLoader(self.data_file_path)
            
            # 初始化分析器
            self.statistical_analyzer = StatisticalAnalyzer(self.config)
            self.markov_analyzer = MarkovChainAnalyzer(self.config)
            
            # 初始化预测引擎
            self.prediction_engine = PredictionEngine(
                self.config,
                self.statistical_analyzer,
                self.markov_analyzer
            )
            
            # 初始化比对引擎
            self.comparison_engine = ComparisonEngine(self.config)
            
            # 初始化导出管理器
            self.export_manager = ExportManager()
            
            print("系统初始化完成！")
            
        except Exception as e:
            print(f"系统初始化失败: {e}")
            sys.exit(1)
    
    def run(self):
        """运行主程序"""
        try:
            self.ui.show_welcome()
            
            while True:
                choice = self.ui.get_main_menu_choice()
                
                if choice == 1:
                    self._run_prediction_mode()
                elif choice == 2:
                    self._run_analysis_mode()
                elif choice == 0:
                    print("感谢使用双色球预测分析系统！")
                    break
                else:
                    print("无效选择，请重新输入。")
                    
        except KeyboardInterrupt:
            print("\n程序被用户中断。")
        except Exception as e:
            print(f"程序运行出错: {e}")
    
    def _run_prediction_mode(self):
        """运行预测选号模式"""
        print("\n=== 预测选号模式 ===")

        # 获取目标期号
        target_period = self.ui.get_target_period_for_prediction(self.data_loader.original_database)

        # 获取远期数据库范围
        long_term_range = self.ui.get_long_term_database_range()

        # 获取近期数据库范围
        short_term_range = self.ui.get_short_term_database_range(long_term_range)
        
        # 加载和处理数据
        long_term_database = self.data_loader.get_database_for_period(target_period, long_term_range)
        short_term_database = self.data_loader.get_database_for_period(target_period, short_term_range)
        
        if long_term_database is None or len(long_term_database) == 0:
            print("远期数据加载失败或数据为空！")
            return
            
        if short_term_database is None or len(short_term_database) == 0:
            print("近期数据加载失败或数据为空！")
            return
        
        # 获取最新一期信息
        latest_period = self.data_loader.get_latest_period(long_term_database)
        self.ui.show_latest_period_info(latest_period, long_term_database)
        
        # 运行预测算法
        predictions, long_term_stats, short_term_stats, long_term_markov, short_term_markov = self._generate_predictions(
            long_term_database, short_term_database, latest_period
        )

        # 显示预测结果
        self.ui.show_prediction_results(predictions)

        # 询问是否保存结果
        if self.ui.ask_save_results():
            self._save_prediction_results(
                predictions, long_term_database, short_term_database,
                long_term_stats, short_term_stats, long_term_markov, short_term_markov
            )
    
    def _run_analysis_mode(self):
        """运行分析比对模式"""
        print("\n=== 分析比对模式 ===")

        # 获取目标期号
        start_period = self.ui.get_target_period_for_analysis()

        # 获取远期数据库范围
        long_term_range = self.ui.get_long_term_database_range()

        # 获取近期数据库范围
        short_term_range = self.ui.get_short_term_database_range(long_term_range)

        # 运行分析比对
        self._run_comparison_analysis(start_period, long_term_range, short_term_range)

    def _generate_predictions(self, long_term_database: pd.DataFrame,
                            short_term_database: pd.DataFrame,
                            latest_period: Dict) -> Tuple[Dict, Dict, Dict, Dict, Dict]:
        """
        生成预测号码

        Args:
            long_term_database: 远期数据库
            short_term_database: 近期数据库
            latest_period: 最新一期数据

        Returns:
            预测结果字典和所有概率表格
        """
        # 运行统计分析
        self.statistical_analyzer.analyze(long_term_database)
        long_term_stats = self.statistical_analyzer.get_probability_tables()

        self.statistical_analyzer.analyze(short_term_database)
        short_term_stats = self.statistical_analyzer.get_probability_tables()

        # 运行马尔科夫链分析
        self.markov_analyzer.analyze(long_term_database, latest_period)
        long_term_markov = self.markov_analyzer.get_probability_tables()

        self.markov_analyzer.analyze(short_term_database, latest_period)
        short_term_markov = self.markov_analyzer.get_probability_tables()

        # 生成预测
        predictions = self.prediction_engine.generate_predictions(
            long_term_stats, short_term_stats,
            long_term_markov, short_term_markov,
            latest_period
        )

        return predictions, long_term_stats, short_term_stats, long_term_markov, short_term_markov

    def _run_comparison_analysis(self, start_period: str,
                               long_term_range: int,
                               short_term_range: int):
        """
        运行比对分析

        Args:
            start_period: 开始期号
            long_term_range: 远期数据库范围
            short_term_range: 近期数据库范围
        """
        try:
            # 获取所有可分析的期号
            analysis_periods = self.data_loader.get_analysis_periods(start_period)

            if not analysis_periods:
                print("没有可分析的期号！")
                return

            # 显示分析开始信息
            self.ui.show_analysis_start_info(len(analysis_periods))

            results = []

            for i, period in enumerate(analysis_periods):
                # 获取当前数据库
                long_term_database = self.data_loader.get_database_for_period(
                    period, long_term_range
                )
                short_term_database = self.data_loader.get_database_for_period(
                    period, short_term_range
                )

                if (long_term_database is None or len(long_term_database) == 0 or
                    short_term_database is None or len(short_term_database) == 0):
                    continue

                # 获取答案数据
                answer_data = self.data_loader.get_answer_data(period)

                if answer_data is None or len(answer_data) == 0:
                    continue

                # 运行预测
                latest_period = self.data_loader.get_latest_period(long_term_database)
                predictions, _, _, _, _ = self._generate_predictions(long_term_database, short_term_database, latest_period)

                # 比对结果
                comparison_result = self.comparison_engine.compare_predictions(
                    predictions, answer_data
                )

                results.append({
                    'period': period,
                    'predictions': predictions,
                    'comparison': comparison_result,
                    'long_term_database_size': len(long_term_database),
                    'short_term_database_size': len(short_term_database),
                    'latest_period': latest_period
                })

                # 每100期显示一次进度
                if (i + 1) % 100 == 0:
                    self.ui.show_analysis_progress(
                        i + 1, len(analysis_periods),
                        len(long_term_database), len(short_term_database), latest_period
                    )

            # 显示最终统计结果
            self.ui.show_final_analysis_results(results)

            # 保存结果
            self._save_analysis_results(results)

        except Exception as e:
            print(f"分析比对过程中出错: {e}")

    def _save_prediction_results(self, predictions: Dict,
                               long_term_database: pd.DataFrame,
                               short_term_database: pd.DataFrame,
                               long_term_stats: Dict,
                               short_term_stats: Dict,
                               long_term_markov: Dict,
                               short_term_markov: Dict):
        """保存预测结果"""
        try:
            # 获取所有概率表格（远期和近期）
            probability_tables = {
                'long_term_statistical': long_term_stats,
                'short_term_statistical': short_term_stats,
                'long_term_markov': long_term_markov,
                'short_term_markov': short_term_markov
            }

            # 导出到Excel
            self.export_manager.export_prediction_results(
                predictions, probability_tables, long_term_database, short_term_database
            )

            print("预测结果已保存到Excel文件！")

        except Exception as e:
            print(f"保存预测结果失败: {e}")

    def _save_analysis_results(self, results: List[Dict]):
        """保存分析结果"""
        try:
            self.export_manager.export_analysis_results(results)
            print("分析结果已保存到Excel文件！")

        except Exception as e:
            print(f"保存分析结果失败: {e}")


def main():
    """主函数"""
    try:
        # 检查数据文件是否存在
        data_file = "lottery_data_all.xlsx"
        if not os.path.exists(data_file):
            print(f"错误: 数据文件 '{data_file}' 不存在！")
            print("请确保数据文件在程序根目录下。")
            sys.exit(1)
        
        # 创建并运行系统
        system = SSQLotterySystem(data_file)
        system.run()
        
    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
