# 双色球彩票预测与分析系统 V2.0

## 系统简介

本系统是一个基于Python开发的双色球彩票预测选号与分析比对程序，实现了以下核心功能：

- **历史出现概率分析**：统计红球和蓝球的历史出现频率
- **跟随性概率分析**：分析相邻两期号码之间的跟随关系
- **马尔科夫链算法**：基于最新一期号码计算下期号码的迁移概率
- **预测选号**：提供两种预测算法生成复式号码
- **分析比对**：将预测结果与实际开奖结果进行比对分析

## 系统架构

```
ssq_lottery_system.py          # 主程序入口
modules/                       # 功能模块目录
├── __init__.py               # 模块包初始化
├── data_loader.py            # 数据加载模块
├── statistical_analyzer.py   # 统计分析模块
├── markov_chain.py           # 马尔科夫链分析模块
├── prediction_engine.py      # 预测引擎模块
├── comparison_engine.py      # 比对引擎模块
├── user_interface.py         # 用户界面模块
└── export_manager.py         # 导出管理模块
```

## 安装要求

### Python版本
- Python 3.7 或更高版本

### 依赖包
```bash
pip install -r requirements.txt
```

主要依赖：
- pandas >= 1.3.0
- numpy >= 1.21.0
- openpyxl >= 3.0.0

## 数据文件要求

系统需要一个名为 `lottery_data_all.xlsx` 的Excel文件，包含以下结构：

- **工作表名称**：`SSQ_data_all`
- **数据列**：
  - A列：期号（5位数字，如25001表示2025年第1期）
  - I-N列：红球号码（6个红球，范围1-33）
  - O列：蓝球号码（1个蓝球，范围1-16）

## 使用方法

### 1. 运行测试
首次使用建议先运行测试脚本：
```bash
python test_system.py
```

### 2. 运行主程序
```bash
python ssq_lottery_system.py
```

### 3. 功能选择

#### 预测选号模式
1. 输入目标期号（预测基于该期号及之前的数据）
2. 设置远期数据库范围（0表示全部数据，正整数表示最近N期）
3. 设置近期数据库范围（正整数，不能大于远期范围）
4. 系统将生成两组预测号码：
   - 第1组：历史出现概率复式
   - 第2组：马尔科夫链复式

#### 分析比对模式
1. 输入开始分析的期号
2. 设置远期和近期数据库范围
3. 系统将自动：
   - 从指定期号开始逐期分析
   - 生成预测号码
   - 与后续6期实际开奖结果比对
   - 统计命中情况

## 核心算法

### 1. 历史出现概率
- 统计指定数据范围内每个号码的出现次数
- 转换为概率分布（概率之和为1）

### 2. 跟随性概率矩阵
- 红球：33×33矩阵，统计相邻两期红球号码的跟随关系
- 蓝球：16×16矩阵，统计相邻两期蓝球号码的跟随关系
- 每列概率之和为1

### 3. 马尔科夫链算法
- 基于最新一期号码和跟随性概率矩阵
- 计算下期各号码的迁移概率
- 红球：矩阵乘法计算33×1概率向量
- 蓝球：直接使用跟随性概率向量

### 4. 预测算法
- **第1组**：历史出现概率复式（远期6个红球+1个蓝球，近期1个不重复红球+1个不重复蓝球）
- **第2组**：马尔科夫链复式（远期6个红球+1个蓝球，近期1个不重复红球+1个不重复蓝球）

### 5. 比对算法
- 将预测的复式号码与连续6期答案数据比对
- 计算红球和蓝球的命中数量
- 统计最大命中情况

## 输出结果

### 预测模式输出
- 控制台显示预测结果
- Excel文件包含：
  - 预测号码详情
  - 各种概率表格
  - 数据库信息

### 分析模式输出
- 控制台显示分析进度和最终统计
- Excel文件包含：
  - 分析结果汇总
  - 详细比对结果
  - 命中统计分布

## 注意事项

1. **数据格式**：确保Excel文件格式正确，期号为5位数字
2. **数据完整性**：系统会自动清理无效数据
3. **内存使用**：大数据量分析时注意内存占用
4. **结果解读**：预测结果仅供参考，不保证中奖

## 文件说明

- `output/`：输出目录，存放生成的Excel结果文件
- `test_system.py`：系统功能测试脚本
- `requirements.txt`：Python依赖包列表
- `README.md`：系统使用说明

## 技术特点

- **模块化设计**：各功能模块独立，便于维护和扩展
- **错误处理**：完善的异常处理和用户输入验证
- **数据验证**：自动验证数据格式和范围
- **结果导出**：支持详细的Excel格式结果导出
- **用户友好**：清晰的交互界面和进度显示

## 版本信息

- **版本**：V2.0
- **开发语言**：Python 3.7+
- **开发时间**：2025年7月
- **作者**：AI Assistant

## 免责声明

本系统仅用于技术研究和学习目的，预测结果不构成任何投注建议。彩票具有随机性，请理性对待，量力而行。
