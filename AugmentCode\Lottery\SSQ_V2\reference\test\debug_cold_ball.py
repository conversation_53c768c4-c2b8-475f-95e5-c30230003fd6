#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试冷球计算问题
"""

import sys
from modules.data_loader import DataLoader
from modules.statistical_analyzer import StatisticalAnalyzer


def debug_cold_ball_calculation():
    """调试冷球计算"""
    print("=== 调试冷球计算 ===")
    
    config = {
        'red_ball_range': (1, 33),
        'blue_ball_range': (1, 16),
        'red_ball_count': 6,
        'blue_ball_count': 1,
        'red_big_ball_threshold': 16,
        'blue_big_ball_threshold': 8,
        'cold_ball_periods': 5,
        'prediction_groups': 24,
        'answer_periods': 6
    }
    
    # 加载数据
    loader = DataLoader('lottery_data_all.xlsx')
    
    # 获取特定期号的数据
    target_period = '25070'
    database_range = 100
    
    current_database = loader.get_database_for_period(target_period, database_range)
    latest_period = loader.get_latest_period(current_database)
    
    print(f"数据库形状: {current_database.shape}")
    print(f"最新期号: {latest_period['period']}")
    print(f"期号范围: {current_database['NO'].min()} - {current_database['NO'].max()}")
    
    # 重置索引
    database_reset = current_database.reset_index(drop=True)
    
    # 找到目标期号位置
    target_period_num = int(latest_period['period'])
    target_index = None
    
    for i in range(len(database_reset)):
        if database_reset.iloc[i]['NO'] == target_period_num:
            target_index = i
            break
    
    print(f"目标期号 {target_period_num} 在重置索引后的位置: {target_index}")
    
    if target_index is not None:
        # 显示目标期号前后的数据
        print(f"\n目标期号前后的数据:")
        start_idx = max(0, target_index - 7)
        end_idx = min(len(database_reset), target_index + 3)
        
        for j in range(start_idx, end_idx):
            row = database_reset.iloc[j]
            red_balls = [int(row[f'r{k}']) for k in range(1, 7)]
            blue_ball = int(row['b'])
            red_str = ' '.join(map(str, red_balls))
            marker = ' <-- 目标期' if j == target_index else ''
            print(f"  索引{j}: 期号{row['NO']} {red_str} + {blue_ball}{marker}")
        
        # 计算之前5期的数据
        if target_index >= 5:
            print(f"\n目标期号之前5期的数据:")
            prev_red_balls = set()
            prev_blue_balls = set()
            
            for j in range(target_index - 5, target_index):
                row = database_reset.iloc[j]
                red_balls = [int(row[f'r{k}']) for k in range(1, 7)]
                blue_ball = int(row['b'])
                red_str = ' '.join(map(str, red_balls))
                print(f"  索引{j}: 期号{row['NO']} {red_str} + {blue_ball}")
                
                for ball in red_balls:
                    prev_red_balls.add(ball)
                prev_blue_balls.add(blue_ball)
            
            print(f"\n之前5期出现的红球: {sorted(prev_red_balls)}")
            print(f"之前5期出现的蓝球: {sorted(prev_blue_balls)}")
            
            # 计算冷球
            all_red = set(range(1, 34))
            all_blue = set(range(1, 17))
            red_cold = sorted(all_red - prev_red_balls)
            blue_cold = sorted(all_blue - prev_blue_balls)
            
            print(f"\n计算的红球冷球: {red_cold}")
            print(f"计算的蓝球冷球: {blue_cold}")
            
            # 计算目标期号的冷球数
            target_red_balls = latest_period['red_balls']
            target_blue_ball = latest_period['blue_ball']
            
            red_cold_count = sum(1 for ball in target_red_balls if ball in red_cold)
            blue_cold_count = 1 if target_blue_ball in blue_cold else 0
            
            print(f"\n目标期号 {target_period_num} 的冷球数:")
            print(f"红球冷球数: {red_cold_count}")
            print(f"蓝球冷球数: {blue_cold_count}")
            
            # 显示具体的冷球
            target_red_cold_balls = [ball for ball in target_red_balls if ball in red_cold]
            print(f"目标期号的红球冷球: {target_red_cold_balls}")
            if target_blue_ball in blue_cold:
                print(f"目标期号的蓝球冷球: {target_blue_ball}")
            else:
                print(f"目标期号的蓝球不是冷球")
        else:
            print(f"目标期号之前的数据不足5期")
    
    # 测试修正后的函数
    print(f"\n=== 测试修正后的函数 ===")
    analyzer = StatisticalAnalyzer(config)
    analyzer.analyze(current_database)
    
    try:
        analysis_red_cold, analysis_blue_cold = analyzer.get_cold_balls_for_analysis(latest_period, current_database)
        print(f"函数计算的红球冷球: {analysis_red_cold}")
        print(f"函数计算的蓝球冷球: {analysis_blue_cold}")
        
        # 计算冷球数
        func_red_cold_count = sum(1 for ball in latest_period['red_balls'] if ball in analysis_red_cold)
        func_blue_cold_count = 1 if latest_period['blue_ball'] in analysis_blue_cold else 0
        
        print(f"函数计算的冷球数: 红{func_red_cold_count} 蓝{func_blue_cold_count}")
        
    except Exception as e:
        print(f"函数调用失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_cold_ball_calculation()
