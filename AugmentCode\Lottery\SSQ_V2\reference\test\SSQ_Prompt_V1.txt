用Python语言开发一个双色球彩票的预测选号与分析比对的程序。

一、术语定义：
1.1 期号 
每期的编号（NO列），由1个5位数组成，其中前两位数字代表年份，比如25表示2025年。后3位数字表示序号，期号越小，表示时间越早。需要注意的是，数据库中的期号并不是按自然数的顺序一直连续的，每年的期号都会从001期开始。
1）最新1期号码：在指定的数据库中，比如在原始数据库或者在被定义的当前数据库中，期号最大的1期的红蓝球号码即为最新1期号码。

1.2 红球与蓝球号码 
在SSQ的7个球中，前6个球为红球，号码范围为1~33。第7个球为蓝球，号码范围为1~16。

1.3 大球数 
1）红球大球数：每期号码中，红球大球数为红球大球号（SSQ中约定红球号码数值在16以上）的个数。 
2）蓝球大球数。每期号码中，蓝球大球数为蓝球大球号（SSQ中约定蓝球号码数值在8以上）的个数。 

1.4 冷球号码与冷球数 
1）红球冷球号码与冷球数 ：每期号码中，红球冷球号码为当前这1期的红球号码中，在之前的最近5期的红球号码以内都未出现过的号码。当前这1期中红球冷球的个数即为红球冷球数。
2）蓝球冷球号码与冷球数 ：每期号码中，蓝球冷球号码为当前这1期的蓝球号码中，在之前的最近5期的蓝球号码以内都未出现过的号码。当前这1期中蓝球冷球的个数即为蓝球冷球数。

1.5 红球重号码与红球重号数
每期的红球号码与上期的红球号码中，连续出现两次的红球号码即为红球重号码。当前这1期中红球重号码的个数即为红球重号数。若预测的号码中红球重号数为1，则意味着，该红球号码中存在1个红球与上一期（最新1期）的红球号码相同。若预测的号码中红球重号数为2，则意味着，该红球号码中存在2个红球与上一期（最新1期）的红球号码相同。依次类推。

二、核心算法：
2.1 统计历史出现概率 
2.1.1 红蓝球号码 
1）红球号码：基于被定义的当前数据库范围，统计所有红球号码的历史出现次数并转化为概率。然后生成2列的表格数据，其中第1列为红球号码（按照号码从小到大排序），第2列为红球号码对应的历史出现概率（第2列概率之和应为1）。 
2） 蓝球号码：基于被定义的当前数据库范围，统计所有蓝球号码的历史出现次数并转化为概率。然后生成2列的表格数据，其中第1列为蓝球号码（按照号码从小到大排序），第2列为蓝球号码对应的历史出现概率（第2列概率之和应为1）。 
2.1.2 红蓝球大球数 
1）红球大球数：基于被定义的当前数据库范围，统计每期号码的红球大球数的历史出现次数并转化为概率。然后生成2列的表格数据，其中第1列为红球大球数（按照从小到大排序），第2列为红球大球数对应的历史出现概率（第2列概率之和应为1）。 
2）蓝球大球数：基于被定义的当前数据库范围，统计每期号码的蓝球大球数的历史出现次数并转化为概率。然后生成2列的表格数据，其中第1列为蓝球大球数（按照从小到大排序），第2列为蓝球大球数对应的历史出现概率（第2列概率之和应为1）。 
2.1.3 红蓝球冷球数 
1）红球冷球数：基于被定义的当前数据库范围，统计每期号码的红球冷球数的历史出现次数并转化为概率。然后生成2列的表格数据，其中第1列为红球冷球数（按照从小到大排序），第2列为红球冷球数对应的历史出现概率（第2列概率之和应为1）。 
2）蓝球冷球数：基于被定义的当前数据库范围，统计每期号码的蓝球冷球数的历史出现次数并转化为概率。然后生成2列的表格数据，其中第1列为蓝球冷球数（按照从小到大排序），第2列为蓝球冷球数对应的历史出现概率（第2列概率之和应为1）。 
2.1.4 红球重号数 
基于被定义的当前数据库范围，统计每期号码的红球重号数的历史出现次数并转化为概率。然后生成2列的表格数据，其中第1列为红球重号数（按照从小到大排序），第2列为红球重号数对应的历史出现概率（第2列概率之和应为1）。

2.2 统计历史跟随性概率 
2.2.1 红蓝球号码 
1）红球号码：基于被定义的当前数据库范围，自上而下统计每相邻两期中不同红球号码之间的历史跟随次数并转化为概率。在SSQ中，红球号码（特性维度）有33个，则需要先建立1个33行33列的空矩阵，自上而下统计当前数据库中每相邻两期，第1期出现红球1时，第2期出现红球1至红球33各自出现的次数，然后转化为概率依次存放在空矩阵中第1列对应的位置。依次类推...，第1期出现红球33时，第2期出现红球1至红球33各自出现的次数，然后转化为概率依次存放在空矩阵中第33列对应的位置。每1列的概率之和都应为1。查表时，如果想查第1期出现红球号码m，第2期出现红球号码n的历史跟随性概率，则直接读取该表格第m列第n行的值即可。 
2）蓝球号码：基于被定义的当前数据库范围，自上而下统计每相邻两期中不同蓝球号码之间的历史跟随次数并转化为概率。在SSQ中，蓝球号码（特性维度）有16个，则需要先建立1个16行16列的空矩阵，自上而下统计当前数据库中每相邻两期，第1期出现蓝球1时，第2期出现蓝球1至蓝球16各自出现的次数，然后转化为概率依次存放在空矩阵中第1列对应的位置。依次类推...，第1期出现蓝球16时，第2期出现蓝球1至蓝球16各自出现的次数，然后转化为概率依次存放在空矩阵中第16列对应的位置。每1列的概率之和都应为1。查表时，如果想查第1期出现蓝球号码m，第2期出现蓝球号码n的历史跟随性概率，则直接读取该表格第m列第n行的值即可。 

2.3 马尔科夫链算法
在进行马尔科夫链算法前，基于被定义的当前数据库范围，定义出最新1期（期号最大）的红蓝球号码，并依次将7个号码值赋值给s1至s7。比如在SSQ中，将最新1期的第1个号码（红球号码）赋值给s1，依次类推，第6个号码（红球号码）赋值给s6，第7个号码（蓝球号码）赋值给s7。 
1）将基于当前被定义的数据库构建的红球号码历史跟随性概率矩阵（33行33列）中，最新1期红球号码相关的列，即第s1、s2、s3、s4、s5与s6列的列向量按从左到右的顺序拼接成1个33行6列的新矩阵1。然后将基于当前数据库构建的红球号码历史出现概率矩阵（33行2列）中，第2列最新1期红球号码相关的行，即第s1、s2、s3、s4、s5与s6行的值按从上到下的顺序拼接成1个6行1列的新向量2。最后将新矩阵1乘以新向量2，得到1个SSQ红球号码马尔科夫链列向量（33行1列）。此列向量第1行至第33行的值依次表示的是基于最新1期SSQ的6个红球号码计算得到的红球号码1~33的号码迁移概率。 
2）将基于当前被定义的数据库构建的蓝球号码历史跟随性概率矩阵（16行16列）中，最新1期蓝球号码相关的列，即第s7列的列向量(16行1列)赋值给SSQ蓝球号码马尔科夫链列向量(16行1列)。此列向量第1行至第16行的值依次表示的是基于最新1期SSQ的1个蓝球号码计算得到的蓝球号码1~16的号码迁移概率。 

2.4 贝叶斯概率算法
在进行贝叶斯概率算法前，基于被定义的当前数据库范围，定义出最新1期（期号最大）的红蓝球号码，并依次将7个号码值赋值给s1至s7。比如在SSQ中，将最新1期的第1个号码（红球号码）赋值给s1，依次类推，第6个号码（红球号码）赋值给s6，第7个号码（蓝球号码）赋值给s7。 
1）将基于当前被定义的数据库构建的红球号码历史跟随性概率矩阵（33行33列）中，最新1期红球号码相关的行，即第s1、s2、s3、s4、s5与s6行的行向量全部相加得到1个1行33列的新向量1。然后将基于当前数据库构建的红球号码历史出现概率矩阵（33行2列）中，第2列的列向量赋值给新向量2（33行1列）。最后构建1个SSQ红球号码贝叶斯概率向量（33行1列），其中第1行的元素值等于新向量1的第1个元素乘以新向量2的第1个元素，第2行的元素值等于新向量1的第2个元素乘以新向量2的第2个元素，依次类推，第33行的元素值等于新向量1的第33个元素乘以新向量2的第33个元素。此列向量第1行至第33行的值依次表示的是基于最新1期SSQ的6个红球号码计算得到的红球号码1~33的贝叶斯概率。 
2）将基于当前被定义的数据库构建的蓝球号码历史跟随性概率矩阵（16行16列）中，最新1期蓝球号码相关的行，即第s7行的行向量赋值给新向量3（1行16列）。然后将基于当前数据库构建的蓝球号码历史出现概率矩阵（16行2列）中，第2列的列向量赋值给新向量4（16行1列）。最后构建1个SSQ蓝球号码贝叶斯概率向量（16行1列），其中第1行的元素值等于新向量3的第1个元素乘以新向量4的第1个元素，第2行的元素值等于新向量3的第2个元素乘以新向量4的第2个元素，依次类推，第16行的元素值等于新向量3的第16个元素乘以新向量4的第16个元素。此列向量第1行至第16行的值依次表示的是基于最新1期SSQ的1个蓝球号码计算得到的蓝球号码1~16的贝叶斯概率。 

2.5 预测算法 
2.5.1 第1组号码的预测算法：历史出现概率 
基于被定义的当前数据库范围，通过统计的历史出现概率，从中分别选出红球号码历史出现概率最大的6个红球号码与蓝球号码历史出现概率最大的1个蓝球号码。组成第1组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.2 第2组号码的预测算法：马尔科夫链 
基于被定义的当前数据库范围，通过马尔科夫链算法，得到基于最新1期红蓝球号码的红球号码1~33的号码迁移概率列向量与蓝球号码1~16的号码迁移概率列向量。从中分别选出红球号码迁移概率最大的6个红球号码（号码分别对应着概率在列向量中的位置）与蓝球号码迁移概率最大的1个蓝球号码（号码对应着概率在列向量中的位置）。组成第2组预测的红蓝球号码（红球号码需按从小到大排序）。 
2.5.3 第3组号码的预测算法：贝叶斯概率 
基于被定义的当前数据库范围，通过贝叶斯概率算法，得到基于最新1期红蓝球号码的红球号码1~33的贝叶斯概率列向量与蓝球号码1~16的贝叶斯概率列向量。从中分别选出红球贝叶斯概率最大的6个红球号码（号码分别对应着概率在列向量中的位置）与蓝球贝叶斯概率最大的1个蓝球号码（号码对应着概率在列向量中的位置）。组成第3组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.4 第4组号码的预测算法：历史出现概率 +大球筛选 
基于被定义的当前数据库范围，通过查找对应的红球大球数历史出现概率表与蓝球大球数历史出现概率表，分别找出对应列中概率最大的红球大球数与蓝球大球数，即第4组预测的号码中要求的红球大球数与蓝球大球数。
1）将通过历史出现概率预测的第1组红蓝球号码设为初始的第4组预测的红蓝球号码。 
2）先检查这6个红球号码中的红球大球数：如果这6个红球号码中的红球大球数大于第4组预测的号码中要求的红球大球数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球大球号码剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球大球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第4组预测的红球号码数量始终为6个。如果这6个红球号码中的红球大球数小于第4组预测的号码中要求的红球大球数，则从33个红球号码中非这6个的其他红球号码中的红球大球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球大球号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第4组预测的红球号码数量始终为6个。
3）再检查这1个蓝球号码中的蓝球大球数：如果这1个蓝球号码中的蓝球大球数大于第4组预测的号码中要求的蓝球大球数，则从16个蓝球号码中的非蓝球大球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第4组预测的蓝球号码数量始终为1个。如果这1个蓝球号码中的蓝球大球数小于第4组预测的号码中要求的蓝球大球数，则从16个蓝球号码中的蓝球大球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第4组预测的蓝球号码数量始终为1个。 
4）基于上述步骤选出的红蓝球号码，组成第4组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.5 第5组号码的预测算法：历史出现概率+冷球筛选  
基于被定义的当前数据库范围，通过查找对应的红球冷球数历史出现概率表与蓝球冷球数历史出现概率表，分别找出对应列中概率最大的红球冷球数与蓝球冷球数，即第5组预测的号码中要求的红球冷球数与蓝球冷球数。
1）将通过历史出现概率预测的第1组红蓝球号码设为初始的第5组预测的红蓝球号码。 
2）先检查这6个红球号码中的红球冷球数：如果这6个红球号码中的红球冷球数大于第5组预测的号码中要求的红球冷球数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球冷球号码剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球冷球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第5组预测的红球号码数量始终为6个。如果这6个红球号码中的红球冷球数小于第5组预测的号码中要求的红球冷球数，则从33个红球号码中非这6个的其他红球号码中的红球冷球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球冷球号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第5组预测的红球号码数量始终为6个。 
3）再检查这1个蓝球号码中的蓝球冷球数：如果这1个蓝球号码中的蓝球冷球数大于第5组预测的号码中要求的蓝球冷球数，则从16个蓝球号码中的非蓝球冷球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第5组预测的蓝球号码数量始终为1个。如果这1个蓝球号码中的蓝球冷球数小于第5组预测的号码中要求的蓝球冷球数，则从16个蓝球号码中的蓝球冷球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第5组预测的蓝球号码数量始终为1个。 
4）基于上述步骤选出的红蓝球号码，组成第5组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.6 第6组号码的预测算法：历史出现概率+红球重号筛选 
基于被定义的当前数据库范围，通过查找红球重号数历史出现概率表，找出对应列中概率最大的红球重号数，即第6组预测的号码中要求的红球重号数。 
1）将通过历史出现概率预测的第1组红蓝球号码设为初始的第6组预测的红蓝球号码。  
2）检查这6个红球号码中的红球重号数：如果这6个红球号码中的红球重号数（与最新1期红球号码相同的数量）大于第6组预测的号码中要求的红球重号数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球重号号码（与最新1期红球号码相同的号码）剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球重号号码（与最新1期红球号码不同的号码）中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第6组预测的红球号码数量始终为6个。如果这6个红球号码中的红球重号数小于第6组预测的号码中要求的红球重号数，则从33个红球号码中非这6个的其他红球号码中的红球重号号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球重号号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第6组预测的红球号码数量始终为6个。  
3）基于上述步骤选出的红蓝球号码，组成第6组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.7 第7组号码的预测算法：马尔科夫链 +大球筛选 
将第4组预测的号码中要求的红球大球数与蓝球大球数定义为第7组预测的号码中要求的红球大球数、蓝球大球数。
1）将通过马尔科夫链算法预测的第2组红蓝球号码设为初始的第7组预测的红蓝球号码。 
2）先检查这6个红球号码中的红球大球数：如果这6个红球号码中的红球大球数大于第7组预测的号码中要求的红球大球数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球大球号码剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球大球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第7组预测的红球号码数量始终为6个。如果这6个红球号码中的红球大球数小于第7组预测的号码中要求的红球大球数，则从33个红球号码中非这6个的其他红球号码中的红球大球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球大球号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第7组预测的红球号码数量始终为6个。
3）再检查这1个蓝球号码中的蓝球大球数：如果这1个蓝球号码中的蓝球大球数大于第7组预测的号码中要求的蓝球大球数，则从16个蓝球号码中的非蓝球大球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第7组预测的蓝球号码数量始终为1个。如果这1个蓝球号码中的蓝球大球数小于第7组预测的号码中要求的蓝球大球数，则从16个蓝球号码中的蓝球大球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第7组预测的蓝球号码数量始终为1个。 
4）基于上述步骤选出的红蓝球号码，组成第7组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.8 第8组号码的预测算法：马尔科夫链+冷球筛选  
将第5组预测的号码中要求的红球冷球数与蓝球冷球数定义为第8组预测的号码中要求的红球冷球数与蓝球冷球数。
1）将通过马尔科夫链算法预测的第2组红蓝球号码设为初始的第8组预测的红蓝球号码。 
2）先检查这6个红球号码中的红球冷球数：如果这6个红球号码中的红球冷球数大于第8组预测的号码中要求的红球冷球数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球冷球号码剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球冷球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第8组预测的红球号码数量始终为6个。如果这6个红球号码中的红球冷球数小于第8组预测的号码中要求的红球冷球数，则从33个红球号码中非这6个的其他红球号码中的红球冷球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球冷球号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第8组预测的红球号码数量始终为6个。 
3）再检查这1个蓝球号码中的蓝球冷球数：如果这1个蓝球号码中的蓝球冷球数大于第8组预测的号码中要求的蓝球冷球数，则从16个蓝球号码中的非蓝球冷球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第8组预测的蓝球号码数量始终为1个。如果这1个蓝球号码中的蓝球冷球数小于第8组预测的号码中要求的蓝球冷球数，则从16个蓝球号码中的蓝球冷球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第8组预测的蓝球号码数量始终为1个。 
4）基于上述步骤选出的红蓝球号码，组成第8组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.9 第9组号码的预测算法：马尔科夫链+红球重号筛选 
将第6组预测的号码中要求的红球重号数定义为第9组预测的号码中要求的红球重号数。 
1）将通过马尔科夫链算法预测的第2组红蓝球号码设为初始的第9组预测的红蓝球号码。  
2）检查这6个红球号码中的红球重号数：如果这6个红球号码中的红球重号数（与最新1期红球号码相同的数量）大于第9组预测的号码中要求的红球重号数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球重号号码（与最新1期红球号码相同的号码）剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球重号号码（与最新1期红球号码不同的号码）中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第9组预测的红球号码数量始终为6个。如果这6个红球号码中的红球重号数小于第9组预测的号码中要求的红球重号数，则从33个红球号码中非这6个的其他红球号码中的红球重号号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球重号号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第9组预测的红球号码数量始终为6个。  
3）基于上述步骤选出的红蓝球号码，组成第9组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.10 第10组号码的预测算法：贝叶斯概率 +大球筛选 
将第4组预测的号码中要求的红球大球数与蓝球大球数定义为第10组预测的号码中要求的红球大球数与蓝球大球数。
1）将通过贝叶斯概率算法预测的第3组红蓝球号码设为初始的第10组预测的红蓝球号码。 
2）先检查这6个红球号码中的红球大球数：如果这6个红球号码中的红球大球数大于第10组预测的号码中要求的红球大球数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球大球号码剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球大球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第10组预测的红球号码数量始终为6个。如果这6个红球号码中的红球大球数小于第10组预测的号码中要求的红球大球数，则从33个红球号码中非这6个的其他红球号码中的红球大球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球大球号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第10组预测的红球号码数量始终为6个。
3）再检查这1个蓝球号码中的蓝球大球数：如果这1个蓝球号码中的蓝球大球数大于第10组预测的号码中要求的蓝球大球数，则从16个蓝球号码中的非蓝球大球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第10组预测的蓝球号码数量始终为1个。如果这1个蓝球号码中的蓝球大球数小于第10组预测的号码中要求的蓝球大球数，则从16个蓝球号码中的蓝球大球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第10组预测的蓝球号码数量始终为1个。 
4）基于上述步骤选出的红蓝球号码，组成第10组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.11 第11组号码的预测算法：贝叶斯概率+冷球筛选  
将第5组预测的号码中要求的红球冷球数与蓝球冷球数定义为第11组预测的号码中要求的红球冷球数与蓝球冷球数。
1）将通过贝叶斯概率算法预测的第3组红蓝球号码设为初始的第11组预测的红蓝球号码。 
2）先检查这6个红球号码中的红球冷球数：如果这6个红球号码中的红球冷球数大于第11组预测的号码中要求的红球冷球数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球冷球号码剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球冷球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第11组预测的红球号码数量始终为6个。如果这6个红球号码中的红球冷球数小于第11组预测的号码中要求的红球冷球数，则从33个红球号码中非这6个的其他红球号码中的红球冷球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球冷球号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第11组预测的红球号码数量始终为6个。 
3）再检查这1个蓝球号码中的蓝球冷球数：如果这1个蓝球号码中的蓝球冷球数大于第11组预测的号码中要求的蓝球冷球数，则从16个蓝球号码中的非蓝球冷球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第11组预测的蓝球号码数量始终为1个。如果这1个蓝球号码中的蓝球冷球数小于第11组预测的号码中要求的蓝球冷球数，则从16个蓝球号码中的蓝球冷球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第11组预测的蓝球号码数量始终为1个。 
4）基于上述步骤选出的红蓝球号码，组成第11组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.12 第12组号码的预测算法：贝叶斯概率+红球重号筛选 
将第6组预测的号码中要求的红球重号数定义为第12组预测的号码中要求的红球重号数。 
1）将通过贝叶斯概率算法预测的第3组红蓝球号码设为初始的第12组预测的红蓝球号码。  
2）检查这6个红球号码中的红球重号数：如果这6个红球号码中的红球重号数（与最新1期红球号码相同的数量）大于第12组预测的号码中要求的红球重号数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球重号号码（与最新1期红球号码相同的号码）剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球重号号码（与最新1期红球号码不同的号码）中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第12组预测的红球号码数量始终为6个。如果这6个红球号码中的红球重号数小于第12组预测的号码中要求的红球重号数，则从33个红球号码中非这6个的其他红球号码中的红球重号号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球重号号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第12组预测的红球号码数量始终为6个。  
3）基于上述步骤选出的红蓝球号码，组成第12组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.13 第13组号码的预测算法：历史出现概率+大球冷球筛选 
1）将第4组预测的号码中要求的红球大球数与蓝球大球数与第5组预测的号码中要求的红球冷球数与蓝球冷球数定义为第13组预测的号码中要求的红球大球数、蓝球大球数、红球冷球数与蓝球冷球数。
2）将通过第4组号码的预测算法预测的红蓝球号码设为初始的第13组预测的红蓝球号码。
3）先检查这6个红球号码中的红球冷球数：如果这6个红球号码中的红球冷球数大于第13组预测的号码中要求的红球冷球数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球冷球号码剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球冷球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第13组预测的红球号码红球大球数等于要求的红球大球数且红球号码数量始终为6个。如果这6个红球号码中的红球冷球数小于第13组预测的号码中要求的红球冷球数，则从33个红球号码中非这6个的其他红球号码中的红球冷球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球冷球号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第13组预测的红球号码红球大球数等于要求的红球大球数且红球号码数量始终为6个。 
4）再检查这1个蓝球号码中的蓝球冷球数：如果这1个蓝球号码中的蓝球冷球数大于第13组预测的号码中要求的蓝球冷球数，则从16个蓝球号码中的非蓝球冷球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第13组预测的蓝球号码蓝球大球数等于要求的蓝球大球数且蓝球号码数量始终为1个。如果这1个蓝球号码中的蓝球冷球数小于第13组预测的号码中要求的蓝球冷球数，则从16个蓝球号码中的蓝球冷球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第13组预测的蓝球号码蓝球大球数等于要求的蓝球大球数且蓝球号码数量始终为1个。 
5）基于上述步骤选出的红蓝球号码，组成第13组预测的红蓝球号码（红球号码需按从小到大排序）。 
2.5.14 第14组号码的预测算法：历史出现概率+大球红球重号筛选 
1）将第4组预测的号码中要求的红球大球数与蓝球大球数与第6组预测的号码中要求的红球重号数定义为第14组预测的号码中要求的红球大球数、蓝球大球数、红球重号数。
2）将通过第4组号码的预测算法预测的红蓝球号码设为初始的第14组预测的红蓝球号码。
3）检查这6个红球号码中的红球重号数：如果这6个红球号码中的红球重号数（与最新1期红球号码相同的数量）大于第14组预测的号码中要求的红球重号数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球重号号码（与最新1期红球号码相同的号码）剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球重号号码（与最新1期红球号码不同的号码）中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第14组预测的红球号码红球大球数等于要求的红球大球数且红球号码数量始终为6个。如果这6个红球号码中的红球重号数小于第14组预测的号码中要求的红球重号数，则从33个红球号码中非这6个的其他红球号码中的红球重号号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球重号号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第14组预测的红球号码红球大球数等于要求的红球大球数且红球号码数量始终为6个。 
4）基于上述步骤选出的红蓝球号码，组成第14组预测的红蓝球号码（红球号码需按从小到大排序）。 
2.5.15 第15组号码的预测算法：历史出现概率+冷球红球重号筛选 
1）将第5组预测的号码中要求的红球冷球数与蓝球冷球数与第6组预测的号码中要求的红球重号数定义为第15组预测的号码中要求的红球冷球数、蓝球冷球数、红球重号数。
2）将通过第5组号码的预测算法预测的红蓝球号码设为初始的第15组预测的红蓝球号码。
3）检查这6个红球号码中的红球重号数：如果这6个红球号码中的红球重号数（与最新1期红球号码相同的数量）大于第15组预测的号码中要求的红球重号数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球重号号码（与最新1期红球号码相同的号码）剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球重号号码（与最新1期红球号码不同的号码）中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第15组预测的红球号码红球冷球数等于要求的红球冷球数且红球号码数量始终为6个。如果这6个红球号码中的红球重号数小于第15组预测的号码中要求的红球重号数，则从33个红球号码中非这6个的其他红球号码中的红球重号号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球重号号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第15组预测的红球号码红球冷球数等于要求的红球冷球数且红球号码数量始终为6个。 
4）基于上述步骤选出的红蓝球号码，组成第15组预测的红蓝球号码（红球号码需按从小到大排序）。 
2.5.16 第16组号码的预测算法：历史出现概率+大球冷球红球重号筛选 
1）将第4组预测的号码中要求的红球大球数与蓝球大球数、第5组预测的号码中要求的红球冷球数与蓝球冷球数与第6组预测的号码中要求的红球重号数定义为第16组预测的号码中要求的红球大球数、蓝球大球数、红球冷球数、蓝球冷球数、红球重号数。
2）将通过第13组号码的预测算法预测的红蓝球号码设为初始的第16组预测的红蓝球号码。
3）检查这6个红球号码中的红球重号数：如果这6个红球号码中的红球重号数（与最新1期红球号码相同的数量）大于第16组预测的号码中要求的红球重号数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球重号号码（与最新1期红球号码相同的号码）剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球重号号码（与最新1期红球号码不同的号码）中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第16组预测的红球号码红球大球数等于要求的红球大球数且红球冷球数等于要求的红球冷球数且红球号码数量始终为6个。如果这6个红球号码中的红球重号数小于第16组预测的号码中要求的红球重号数，则从33个红球号码中非这6个的其他红球号码中的红球重号号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球重号号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第16组预测的红球号码红球大球数等于要求的红球大球数且红球冷球数等于要求的红球冷球数且红球号码数量始终为6个。 
4）基于上述步骤选出的红蓝球号码，组成第16组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.17 第17组号码的预测算法：马尔科夫链+大球冷球筛选 
1）将第4组预测的号码中要求的红球大球数与蓝球大球数与第5组预测的号码中要求的红球冷球数与蓝球冷球数定义为第17组预测的号码中要求的红球大球数、蓝球大球数、红球冷球数与蓝球冷球数。
2）将通过第7组号码的预测算法预测的红蓝球号码设为初始的第17组预测的红蓝球号码。
3）先检查这6个红球号码中的红球冷球数：如果这6个红球号码中的红球冷球数大于第17组预测的号码中要求的红球冷球数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球冷球号码剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球冷球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第17组预测的红球号码红球大球数等于要求的红球大球数且红球号码数量始终为6个。如果这6个红球号码中的红球冷球数小于第17组预测的号码中要求的红球冷球数，则从33个红球号码中非这6个的其他红球号码中的红球冷球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球冷球号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第17组预测的红球号码红球大球数等于要求的红球大球数且红球号码数量始终为6个。 
4）再检查这1个蓝球号码中的蓝球冷球数：如果这1个蓝球号码中的蓝球冷球数大于第17组预测的号码中要求的蓝球冷球数，则从16个蓝球号码中的非蓝球冷球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第17组预测的蓝球号码蓝球大球数等于要求的蓝球大球数且蓝球号码数量始终为1个。如果这1个蓝球号码中的蓝球冷球数小于第17组预测的号码中要求的蓝球冷球数，则从16个蓝球号码中的蓝球冷球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第17组预测的蓝球号码蓝球大球数等于要求的蓝球大球数且蓝球号码数量始终为1个。 
5）基于上述步骤选出的红蓝球号码，组成第17组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.18 第18组号码的预测算法：马尔科夫链+大球红球重号筛选 
1）将第4组预测的号码中要求的红球大球数与蓝球大球数与第6组预测的号码中要求的红球重号数定义为第18组预测的号码中要求的红球大球数、蓝球大球数、红球重号数。
2）将通过第7组号码的预测算法预测的红蓝球号码设为初始的第18组预测的红蓝球号码。
3）检查这6个红球号码中的红球重号数：如果这6个红球号码中的红球重号数（与最新1期红球号码相同的数量）大于第18组预测的号码中要求的红球重号数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球重号号码（与最新1期红球号码相同的号码）剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球重号号码（与最新1期红球号码不同的号码）中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第18组预测的红球号码红球大球数等于要求的红球大球数且红球号码数量始终为6个。如果这6个红球号码中的红球重号数小于第18组预测的号码中要求的红球重号数，则从33个红球号码中非这6个的其他红球号码中的红球重号号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球重号号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第18组预测的红球号码红球大球数等于要求的红球大球数且红球号码数量始终为6个。 
4）基于上述步骤选出的红蓝球号码，组成第18组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.19 第19组号码的预测算法：马尔科夫链+冷球红球重号筛选 
1）将第5组预测的号码中要求的红球冷球数与蓝球冷球数与第6组预测的号码中要求的红球重号数定义为第19组预测的号码中要求的红球冷球数、蓝球冷球数、红球重号数。
2）将通过第8组号码的预测算法预测的红蓝球号码设为初始的第19组预测的红蓝球号码。
3）检查这6个红球号码中的红球重号数：如果这6个红球号码中的红球重号数（与最新1期红球号码相同的数量）大于第19组预测的号码中要求的红球重号数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球重号号码（与最新1期红球号码相同的号码）剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球重号号码（与最新1期红球号码不同的号码）中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第19组预测的红球号码红球冷球数等于要求的红球冷球数且红球号码数量始终为6个。如果这6个红球号码中的红球重号数小于第19组预测的号码中要求的红球重号数，则从33个红球号码中非这6个的其他红球号码中的红球重号号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球重号号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第19组预测的红球号码红球冷球数等于要求的红球冷球数且红球号码数量始终为6个。 
4）基于上述步骤选出的红蓝球号码，组成第19组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.20 第20组号码的预测算法：马尔科夫链+大球冷球红球重号筛选 
1）将第4组预测的号码中要求的红球大球数与蓝球大球数、第5组预测的号码中要求的红球冷球数与蓝球冷球数与第6组预测的号码中要求的红球重号数定义为第20组预测的号码中要求的红球大球数、蓝球大球数、红球冷球数、蓝球冷球数、红球重号数。
2）将通过第17组号码的预测算法预测的红蓝球号码设为初始的第20组预测的红蓝球号码。
3）检查这6个红球号码中的红球重号数：如果这6个红球号码中的红球重号数（与最新1期红球号码相同的数量）大于第20组预测的号码中要求的红球重号数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球重号号码（与最新1期红球号码相同的号码）剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球重号号码（与最新1期红球号码不同的号码）中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第20组预测的红球号码红球大球数等于要求的红球大球数且红球冷球数等于要求的红球冷球数且红球号码数量始终为6个。如果这6个红球号码中的红球重号数小于第20组预测的号码中要求的红球重号数，则从33个红球号码中非这6个的其他红球号码中的红球重号号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球重号号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第20组预测的红球号码红球大球数等于要求的红球大球数且红球冷球数等于要求的红球冷球数且红球号码数量始终为6个。 
4）基于上述步骤选出的红蓝球号码，组成第20组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.21 第21组号码的预测算法：贝叶斯概率+大球冷球筛选 
1）将第4组预测的号码中要求的红球大球数与蓝球大球数与第5组预测的号码中要求的红球冷球数与蓝球冷球数定义为第21组预测的号码中要求的红球大球数、蓝球大球数、红球冷球数与蓝球冷球数。
2）将通过第10组号码的预测算法预测的红蓝球号码设为初始的第21组预测的红蓝球号码。
3）先检查这6个红球号码中的红球冷球数：如果这6个红球号码中的红球冷球数大于第21组预测的号码中要求的红球冷球数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球冷球号码剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球冷球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第21组预测的红球号码红球大球数等于要求的红球大球数且红球号码数量始终为6个。如果这6个红球号码中的红球冷球数小于第21组预测的号码中要求的红球冷球数，则从33个红球号码中非这6个的其他红球号码中的红球冷球号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球冷球号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第21组预测的红球号码红球大球数等于要求的红球大球数且红球号码数量始终为6个。 
4）再检查这1个蓝球号码中的蓝球冷球数：如果这1个蓝球号码中的蓝球冷球数大于第21组预测的号码中要求的蓝球冷球数，则从16个蓝球号码中的非蓝球冷球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第21组预测的蓝球号码蓝球大球数等于要求的蓝球大球数且蓝球号码数量始终为1个。如果这1个蓝球号码中的蓝球冷球数小于第21组预测的号码中要求的蓝球冷球数，则从16个蓝球号码中的蓝球冷球号码中按蓝球号码历史出现概率从大到小的顺序，进行相应替换，并同时确保第21组预测的蓝球号码蓝球大球数等于要求的蓝球大球数且蓝球号码数量始终为1个。 
5）基于上述步骤选出的红蓝球号码，组成第21组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.22 第22组号码的预测算法：贝叶斯概率+大球红球重号筛选 
1）将第4组预测的号码中要求的红球大球数与蓝球大球数与第6组预测的号码中要求的红球重号数定义为第22组预测的号码中要求的红球大球数、蓝球大球数、红球重号数。
2）将通过第10组号码的预测算法预测的红蓝球号码设为初始的第22组预测的红蓝球号码。
3）检查这6个红球号码中的红球重号数：如果这6个红球号码中的红球重号数（与最新1期红球号码相同的数量）大于第22组预测的号码中要求的红球重号数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球重号号码（与最新1期红球号码相同的号码）剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球重号号码（与最新1期红球号码不同的号码）中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第22组预测的红球号码红球大球数等于要求的红球大球数且红球号码数量始终为6个。如果这6个红球号码中的红球重号数小于第22组预测的号码中要求的红球重号数，则从33个红球号码中非这6个的其他红球号码中的红球重号号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球重号号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第22组预测的红球号码红球大球数等于要求的红球大球数且红球号码数量始终为6个。 
4）基于上述步骤选出的红蓝球号码，组成第22组预测的红蓝球号码（红球号码需按从小到大排序）。 
2.5.23 第23组号码的预测算法：贝叶斯概率+冷球红球重号筛选 
1）将第5组预测的号码中要求的红球冷球数与蓝球冷球数与第6组预测的号码中要求的红球重号数定义为第23组预测的号码中要求的红球冷球数、蓝球冷球数、红球重号数。
2）将通过第11组号码的预测算法预测的红蓝球号码设为初始的第23组预测的红蓝球号码。
3）检查这6个红球号码中的红球重号数：如果这6个红球号码中的红球重号数（与最新1期红球号码相同的数量）大于第23组预测的号码中要求的红球重号数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球重号号码（与最新1期红球号码相同的号码）剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球重号号码（与最新1期红球号码不同的号码）中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第23组预测的红球号码红球冷球数等于要求的红球冷球数且红球号码数量始终为6个。如果这6个红球号码中的红球重号数小于第23组预测的号码中要求的红球重号数，则从33个红球号码中非这6个的其他红球号码中的红球重号号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球重号号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第23组预测的红球号码红球冷球数等于要求的红球冷球数且红球号码数量始终为6个。 
4）基于上述步骤选出的红蓝球号码，组成第23组预测的红蓝球号码（红球号码需按从小到大排序）。
2.5.24 第24组号码的预测算法：贝叶斯概率+大球冷球红球重号筛选 
1）将第4组预测的号码中要求的红球大球数与蓝球大球数、第5组预测的号码中要求的红球冷球数与蓝球冷球数与第6组预测的号码中要求的红球重号数定义为第24组预测的号码中要求的红球大球数、蓝球大球数、红球冷球数、蓝球冷球数、红球重号数。
2）将通过第21组号码的预测算法预测的红蓝球号码设为初始的第24组预测的红蓝球号码。
3）检查这6个红球号码中的红球重号数：如果这6个红球号码中的红球重号数（与最新1期红球号码相同的数量）大于第24组预测的号码中要求的红球重号数，则按红球号码历史出现概率从小到大的顺序，将这6个红球号码中多余的红球重号号码（与最新1期红球号码相同的号码）剔除至相等，并从33个红球号码中非这6个的其他红球号码中的非红球重号号码（与最新1期红球号码不同的号码）中按红球号码历史出现概率从大到小的顺序，进行相应补充，并同时确保第24组预测的红球号码红球大球数等于要求的红球大球数且红球冷球数等于要求的红球冷球数且红球号码数量始终为6个。如果这6个红球号码中的红球重号数小于第24组预测的号码中要求的红球重号数，则从33个红球号码中非这6个的其他红球号码中的红球重号号码中按红球号码历史出现概率从大到小的顺序，进行相应补充至相等，再将补充之后形成的红球号码中多余的非红球重号号码按红球号码历史出现概率从小到大的顺序剔除，并同时确保第24组预测的红球号码红球大球数等于要求的红球大球数且红球冷球数等于要求的红球冷球数且红球号码数量始终为6个。
4）基于上述步骤选出的红蓝球号码，组成第24组预测的红蓝球号码（红球号码需按从小到大排序）。 

2.6 比对算法 用预测的若干组红蓝球号码与答案数据库比对时，分别将预测的每一组红蓝球号码（红球+蓝球，共计7个号码）与答案数据中的6组红蓝球号码（每一组也是7个号码）一一比对： 
1）找出红球号码相等与蓝球号码相等的数量之和最大的数字作为最大命中情况。比如在某一组红蓝球号码与答案数据的6组红蓝球号码一一比对时，红球号码相等与蓝球号码相等的数量之和分别是2,3,3,4,5,5，则该组预测的红蓝球号码最大命中情况为中了5个球。 

三、功能定义：
3.1 读取数据
1）读取指定excel文件（根目录下的"lottery_data_all.xlsx"）中，"SSQ_data_all"标签页下A列至O列所有数据，将其中的A列、I列至O列数据（共计8列数据）保存为合适类型的变量，变量名为ssqhistory_all，作为原始数据库。其中A列为期号列，I列至N列均为红球列，O列为蓝球列。
2）所有数据，按第一列（期号列）序号从小到大往下排列。

3.2 用户交互 
询问用户想选择哪种功能：1）预测选号、2）分析比对。
3.2.1 预测选号
1）定义当前数据库范围：若用户选择预测选号，则先询问用户，根据用户输入来指定当前数据库的数据范围。如果用户输入0，则将原始数据库赋值给当前数据库。如果用户输入一个非零正整数，比如500，则将原始数据库中，最新1期及之前的499期红蓝球数据，总共500期的红蓝球号码作为当前数据库。
2）打印最新1期号码相关信息：基于被定义的当前数据库范围，打印最新1期的红蓝球号码（格式参考：最新一期的号码为：期号 红球号码 + 蓝球号码）。并基于最新1期号码，打印红球冷球号码（格式参考：红球冷球号码为：红球冷球号码）、蓝球冷球号码（格式参考：蓝球冷球号码为：蓝球冷球号码）。
3）运行预测算法：基于被定义的当前数据库范围，通过预测算法，预测出若干组红蓝球号码。
4）打印必要信息：在运行预测算法完成之后，基于被定义的当前数据库范围，打印如下信息，包括：第4组预测的号码中要求的红球大球数与蓝球大球数、第5组预测的号码中要求的红球冷球数与蓝球冷球数、第6组预测的号码中要求的红球重号数、所有（24组）预测的号码（格式参考：第几组预测的号码为：预测方法 红球号码 + 蓝球号码）等。
5）保存结果：询问用户是否保存统计表格，如果用户选择是，则将程序中涉及到的所有组预测的号码（格式参考：第几组预测的号码为：预测方法 红球号码 + 蓝球号码）与所有概率表格（比如：历史跟随性概率表格、所有的历史出现概率表格、红蓝球号码的马尔可夫链（号码迁移）概率与贝叶斯概率）等，都打印到根目录下的Excel文件中保存。
3.2.2 分析比对
1）定义目标期号：若用户选择分析比对，则先询问用户，想从哪一期号码开始。把用户输入的期号作为目标期号。
2）定义当前数据库范围：当用户定义目标期号之后，则接着询问用户，根据用户输入来指定当前数据库的数据范围。如果用户输入0，则将原始数据库中，目标期号及之前的所有数据赋值给当前数据库。如果用户输入一个非零正整数，比如500，则将原始数据库中，目标期号及之前的499期红蓝球数据，总共500期的红蓝球号码作为当前数据库（如果目标期号及之前的红蓝球数据不足，则需要显示真实的期数）。
3）定义当前答案数据：将目标期号（每次分析比对时）之后的连续6期红蓝球号码作为答案数据。比如在分析比对第23001期时，将23002~23007期，总共6期的红球与蓝球号码作为答案数据。在分析比对第23002期时，将23003~23008期，总共6期的红球与蓝球号码作为答案数据，以此类推。
4）运行比对算法：从用户输入的目标期号开始，先基于被定义的当前数据库范围，通过预测算法，预测出若干组红蓝球号码。然后与当前答案数据按照比对算法进行比对，统计本次预测的所有组号码中红球号码与蓝球号码的每一组最大命中情况。 
5）循环运行：当完成上一步比对算法后，按照原始数据库的期号先后顺序，将上一次分析比对的期号往下顺延至后1期以作为新的分析比对的目标期号（注意：在分析比对中，在完成每年的最后一期分析后，需要正确跳转至下一年的第一期，如果上一期的目标期号是23001，则本期的目标期号是23002，如果上一期的目标期号是23151，则本期的目标期号是24001）。并依据用户一开始定义的当前数据库范围（此时不需要用户再重复输入）和更新后的目标期号，来更新当前数据库与当前答案数据。然后从更新后的目标期号开始，再基于更新后的当前数据库，通过预测算法，预测出若干组红蓝球号码，并与更新后的当前答案数据按照比对算法进行比对，统计本次预测的所有组（24组）号码中红球号码与蓝球号码的最大命中情况。 重复这一步操作，如果原始数据库不足，则分析到合适的期号为止。 
6）打印必要信息：程序在运行分析比对开始时需要在一开始打印显示：需要分析比对的总期数（根据用户指定的开始期号计算出需要分析的总期数），然后每当完成50期分析比对时，打印显示当前已完成多少期的分析、当前数据库包含多少期数据、当前最新1期的号码、最新1期号码的红蓝球大球数、红蓝球冷球数、红球重号数与红蓝球冷球号码等这些信息。 在完成所有的分析比对之后，再最终打印显示每一组预测号码的最大命中情况分布统计结果。
5）保存结果：将所有比对结果，按照要求保存在根目录下的Excel文件中。如果用户中断了分析的进程，则需要将已分析完的步骤结果保存。保存的内容要求包括：分析的期号、预测的所有组（24组）红蓝球号码（分行保存）、每一组预测号码的红蓝球号码最大命中情况与在最大命中情况下的蓝球号码命中状态。 

四、程序要求
4.1 模块化
相关代码片段请按模块化的思路来构建，便于后续修订与维护。并在保证实现本文中的所有需求的前提下，优先使用更高效的代码实现，以提高程序整体的运行效率。

4.2 注释
程序中所有代码都需要有注释说明。 

4.3 读取数据
读取的数据中，自动清空无效数据或空数据行。 

4.4 用户交互
用户交互中，如果用户输入错误，则提醒用户重新输入。 

4.5 数据打印显示
打印的红蓝球号码（整数）显示格式应为红球号码1 红球号码2 红球号码3 红球号码4 红球号码5 红球号码6 + 蓝球号码，比如：10 12 14 19 20 21 + 15。