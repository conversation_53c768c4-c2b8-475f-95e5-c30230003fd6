#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试25067期的高命中数打印
"""

import sys
from ssq_lottery_system import SSQLotterySystem


def test_25067_specifically():
    """专门测试25067期的高命中数打印"""
    print("=== 专门测试25067期的高命中数打印 ===")
    
    # 初始化系统
    system = SSQLotterySystem('lottery_data_all.xlsx')
    
    try:
        # 只分析25067期
        period = '25067'
        database_range = 99
        
        print(f"分析期号: {period}")
        print(f"数据库范围: {database_range}期")
        
        # 获取当前数据库
        current_database = system.data_loader.get_database_for_period(
            period, database_range
        )
        
        # 获取答案数据
        answer_data = system.data_loader.get_answer_data(period)
        
        # 运行预测
        latest_period = system.data_loader.get_latest_period(current_database)
        
        # 分析
        system.statistical_analyzer.analyze(current_database)
        system.markov_analyzer.analyze(current_database, latest_period)
        system.bayesian_analyzer.analyze(current_database, latest_period)
        
        # 生成预测
        predictions = system.prediction_engine.generate_all_predictions(
            current_database, latest_period
        )
        
        # 比对结果
        comparison_result = system.comparison_engine.compare_predictions(
            predictions, answer_data
        )
        
        # 构造results数据结构
        results = [{
            'period': period,
            'predictions': predictions,
            'comparison': comparison_result,
            'database_size': len(current_database),
            'latest_period': latest_period
        }]
        
        # 先显示所有组的情况
        print(f"\n所有组的命中情况:")
        for group_id in range(1, 25):
            if group_id in predictions and group_id in comparison_result:
                prediction = predictions[group_id]
                group_result = comparison_result[group_id]
                max_hit = group_result['max_hit']
                
                red_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
                print(f"  第{group_id:2d}组: {red_str} + {prediction['blue_ball']:2d} -> "
                      f"红{max_hit['red_hits']}+蓝{max_hit['blue_hits']}={max_hit['total_hits']}球 "
                      f"(期号{max_hit['period']})")
        
        # 找出6球以上命中的组
        high_hit_groups = []
        for group_id in range(1, 25):
            if group_id in predictions and group_id in comparison_result:
                prediction = predictions[group_id]
                group_result = comparison_result[group_id]
                max_hit = group_result['max_hit']
                
                if max_hit['total_hits'] >= 6:
                    high_hit_groups.append((group_id, prediction, max_hit))
        
        if high_hit_groups:
            print(f"\n发现 {len(high_hit_groups)} 个高命中组:")
            for group_id, prediction, max_hit in high_hit_groups:
                red_str = ' '.join(f"{ball:2d}" for ball in prediction['red_balls'])
                print(f"  第{group_id}组: {red_str} + {prediction['blue_ball']} -> {max_hit['total_hits']}球")
                
                # 详细检查这个组的数据
                print(f"    预测红球原始数据: {prediction['red_balls']}")
                print(f"    预测蓝球原始数据: {prediction['blue_ball']} (类型: {type(prediction['blue_ball'])})")
                print(f"    答案红球: {max_hit['answer_red_balls']}")
                print(f"    答案蓝球: {max_hit['answer_blue_ball']} (类型: {type(max_hit['answer_blue_ball'])})")
        else:
            print(f"\n没有发现6球以上命中的组")
        
        # 调用高命中数详细信息打印功能
        print(f"\n=== 调用 _print_high_hit_details 方法 ===")
        system._print_high_hit_details(results)
        
        return True
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("专门测试25067期的高命中数打印")
    print("=" * 60)
    
    success = test_25067_specifically()
    
    print("\n" + "=" * 60)
    if success:
        print("🎯 测试完成！")
        print("请对比上面的打印结果与您在终端中看到的结果")
        print("如果不一致，说明可能有数据混乱或其他问题")
    else:
        print("❌ 测试失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
